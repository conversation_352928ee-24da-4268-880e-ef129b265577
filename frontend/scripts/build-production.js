#!/usr/bin/env node

/**
 * Production Build Script
 * Optimized build process with compression, analysis, and CDN preparation
 */

import { execSync } from 'child_process'
import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)
const projectRoot = path.dirname(__dirname)

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
}

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`)
}

function formatBytes(bytes) {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

function getDirectorySize(dirPath) {
  let totalSize = 0
  
  function calculateSize(currentPath) {
    try {
      const stats = fs.statSync(currentPath)
      
      if (stats.isFile()) {
        totalSize += stats.size
      } else if (stats.isDirectory()) {
        const files = fs.readdirSync(currentPath)
        files.forEach(file => {
          calculateSize(path.join(currentPath, file))
        })
      }
    } catch (error) {
      // Skip files that can't be read
    }
  }
  
  try {
    calculateSize(dirPath)
  } catch (error) {
    return 0
  }
  
  return totalSize
}

function analyzeBundle() {
  log('\n📊 Bundle Analysis', 'cyan')
  log('='.repeat(50), 'cyan')
  
  const distPath = path.join(projectRoot, 'dist')
  if (!fs.existsSync(distPath)) {
    log('❌ dist directory not found', 'red')
    return
  }
  
  const totalSize = getDirectorySize(distPath)
  log(`📦 Total bundle size: ${formatBytes(totalSize)}`, 'green')
  
  // Analyze individual files
  const files = fs.readdirSync(distPath, { recursive: true })
  const fileStats = []
  
  files.forEach(file => {
    const filePath = path.join(distPath, file)
    try {
      const stats = fs.statSync(filePath)
      if (stats.isFile()) {
        fileStats.push({
          name: file,
          size: stats.size,
          compressed: fs.existsSync(`${filePath}.gz`),
          brotli: fs.existsSync(`${filePath}.br`)
        })
      }
    } catch (error) {
      // Skip files that can't be read
    }
  })
  
  // Sort by size (largest first)
  fileStats.sort((a, b) => b.size - a.size)
  
  log('\n📋 Largest files:', 'blue')
  fileStats.slice(0, 10).forEach(file => {
    const compressionInfo = []
    if (file.compressed) compressionInfo.push('gz')
    if (file.brotli) compressionInfo.push('br')
    const compression = compressionInfo.length > 0 ? ` (${compressionInfo.join(', ')})` : ''
    
    log(`  ${file.name}: ${formatBytes(file.size)}${compression}`, 'blue')
  })
  
  // Calculate compression ratios
  const jsFiles = fileStats.filter(f => f.name.endsWith('.js'))
  const cssFiles = fileStats.filter(f => f.name.endsWith('.css'))
  
  log(`\n📈 File type breakdown:`, 'yellow')
  log(`  JavaScript files: ${jsFiles.length} (${formatBytes(jsFiles.reduce((sum, f) => sum + f.size, 0))})`, 'yellow')
  log(`  CSS files: ${cssFiles.length} (${formatBytes(cssFiles.reduce((sum, f) => sum + f.size, 0))})`, 'yellow')
  
  // Check for compression
  const compressedFiles = fileStats.filter(f => f.compressed || f.brotli)
  log(`  Compressed files: ${compressedFiles.length}/${fileStats.length}`, 'yellow')
}

function generateCDNManifest() {
  log('\n🌐 Generating CDN Manifest', 'cyan')
  log('='.repeat(50), 'cyan')
  
  const distPath = path.join(projectRoot, 'dist')
  const manifestPath = path.join(distPath, 'cdn-manifest.json')
  
  const files = fs.readdirSync(distPath, { recursive: true })
  const cdnFiles = []
  
  files.forEach(file => {
    const filePath = path.join(distPath, file)
    try {
      const stats = fs.statSync(filePath)
      if (stats.isFile() && !file.includes('cdn-manifest.json')) {
        const hasGzip = fs.existsSync(`${filePath}.gz`)
        const hasBrotli = fs.existsSync(`${filePath}.br`)
        
        cdnFiles.push({
          path: file.replace(/\\/g, '/'), // Normalize path separators
          size: stats.size,
          lastModified: stats.mtime.toISOString(),
          contentType: getContentType(file),
          encoding: {
            gzip: hasGzip,
            brotli: hasBrotli
          },
          cacheControl: getCacheControl(file)
        })
      }
    } catch (error) {
      // Skip files that can't be read
    }
  })
  
  const manifest = {
    version: '1.0.0',
    buildTime: new Date().toISOString(),
    totalFiles: cdnFiles.length,
    totalSize: cdnFiles.reduce((sum, f) => sum + f.size, 0),
    files: cdnFiles
  }
  
  fs.writeFileSync(manifestPath, JSON.stringify(manifest, null, 2))
  log(`✅ CDN manifest generated: ${cdnFiles.length} files`, 'green')
}

function getContentType(filename) {
  const ext = path.extname(filename).toLowerCase()
  const contentTypes = {
    '.js': 'application/javascript',
    '.css': 'text/css',
    '.html': 'text/html',
    '.json': 'application/json',
    '.png': 'image/png',
    '.jpg': 'image/jpeg',
    '.jpeg': 'image/jpeg',
    '.gif': 'image/gif',
    '.svg': 'image/svg+xml',
    '.webp': 'image/webp',
    '.woff': 'font/woff',
    '.woff2': 'font/woff2',
    '.ttf': 'font/ttf',
    '.eot': 'application/vnd.ms-fontobject'
  }
  return contentTypes[ext] || 'application/octet-stream'
}

function getCacheControl(filename) {
  const ext = path.extname(filename).toLowerCase()
  
  // Static assets with hash - cache for 1 year
  if (filename.includes('-') && (ext === '.js' || ext === '.css')) {
    return 'public, max-age=31536000, immutable'
  }
  
  // Images - cache for 1 month
  if (['.png', '.jpg', '.jpeg', '.gif', '.svg', '.webp'].includes(ext)) {
    return 'public, max-age=2592000'
  }
  
  // Fonts - cache for 1 year
  if (['.woff', '.woff2', '.ttf', '.eot'].includes(ext)) {
    return 'public, max-age=31536000'
  }
  
  // HTML files - no cache
  if (ext === '.html') {
    return 'no-cache, no-store, must-revalidate'
  }
  
  // Default - cache for 1 hour
  return 'public, max-age=3600'
}

function validateBuild() {
  log('\n✅ Build Validation', 'cyan')
  log('='.repeat(50), 'cyan')
  
  const distPath = path.join(projectRoot, 'dist')
  const requiredFiles = ['index.html', 'assets']
  
  let isValid = true
  
  requiredFiles.forEach(file => {
    const filePath = path.join(distPath, file)
    if (!fs.existsSync(filePath)) {
      log(`❌ Missing required file/directory: ${file}`, 'red')
      isValid = false
    } else {
      log(`✅ Found: ${file}`, 'green')
    }
  })
  
  // Check for source maps in production
  const files = fs.readdirSync(distPath, { recursive: true })
  const sourceMaps = files.filter(f => f.endsWith('.map'))
  
  if (sourceMaps.length > 0) {
    log(`⚠️  Found ${sourceMaps.length} source map files (consider removing for production)`, 'yellow')
  }
  
  // Check bundle size limits
  const totalSize = getDirectorySize(distPath)
  const maxSize = 10 * 1024 * 1024 // 10MB
  
  if (totalSize > maxSize) {
    log(`⚠️  Bundle size (${formatBytes(totalSize)}) exceeds recommended limit (${formatBytes(maxSize)})`, 'yellow')
  } else {
    log(`✅ Bundle size (${formatBytes(totalSize)}) is within limits`, 'green')
  }
  
  return isValid
}

function main() {
  log('🚀 Production Build Process', 'bright')
  log('='.repeat(60), 'bright')
  
  try {
    // Clean previous build
    log('\n🧹 Cleaning previous build...', 'yellow')
    const distPath = path.join(projectRoot, 'dist')
    if (fs.existsSync(distPath)) {
      fs.rmSync(distPath, { recursive: true, force: true })
    }
    
    // Run TypeScript check
    log('\n🔍 Running TypeScript check...', 'yellow')
    try {
      execSync('npx tsc --noEmit', { 
        cwd: projectRoot, 
        stdio: 'inherit' 
      })
      log('✅ TypeScript check passed', 'green')
    } catch (error) {
      log('⚠️  TypeScript check failed, continuing with build...', 'yellow')
    }
    
    // Run build
    log('\n🔨 Building application...', 'yellow')
    execSync('npm run build', { 
      cwd: projectRoot, 
      stdio: 'inherit' 
    })
    log('✅ Build completed', 'green')
    
    // Analyze bundle
    analyzeBundle()
    
    // Generate CDN manifest
    generateCDNManifest()
    
    // Validate build
    const isValid = validateBuild()
    
    if (isValid) {
      log('\n🎉 Production build completed successfully!', 'green')
      log('📊 Check dist/stats.html for detailed bundle analysis', 'blue')
      log('🌐 CDN manifest available at dist/cdn-manifest.json', 'blue')
    } else {
      log('\n❌ Build validation failed', 'red')
      process.exit(1)
    }
    
  } catch (error) {
    log(`\n❌ Build failed: ${error.message}`, 'red')
    process.exit(1)
  }
}

// Run the build process
main()
