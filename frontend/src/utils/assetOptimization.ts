/**
 * Asset Optimization Utilities
 * Comprehensive asset loading and optimization strategies
 */

import React from 'react'

// Image optimization configuration
export const IMAGE_OPTIMIZATION = {
  // Supported formats in order of preference
  FORMATS: ['webp', 'avif', 'png', 'jpg', 'jpeg'] as const,
  
  // Quality settings for different use cases
  QUALITY: {
    thumbnail: 60,
    preview: 75,
    full: 85,
    print: 95
  } as const,
  
  // Size breakpoints for responsive images
  BREAKPOINTS: {
    xs: 320,
    sm: 640,
    md: 768,
    lg: 1024,
    xl: 1280,
    '2xl': 1536
  } as const,
  
  // Maximum file sizes (in bytes)
  MAX_SIZES: {
    thumbnail: 50 * 1024, // 50KB
    preview: 200 * 1024, // 200KB
    full: 1024 * 1024, // 1MB
    print: 5 * 1024 * 1024 // 5MB
  } as const
}

// Font optimization configuration
export const FONT_OPTIMIZATION = {
  // Font display strategies
  DISPLAY: {
    swap: 'swap', // Show fallback immediately, swap when loaded
    fallback: 'fallback', // Brief invisible period, then fallback
    optional: 'optional' // Use only if cached or loads quickly
  } as const,
  
  // Font formats in order of preference
  FORMATS: ['woff2', 'woff', 'ttf'] as const,
  
  // Preload critical fonts
  PRELOAD_FONTS: [
    '/fonts/inter-var.woff2',
    '/fonts/arabic-font.woff2'
  ] as const
}

// Asset loading strategies
export const LOADING_STRATEGIES = {
  // Critical assets - load immediately
  CRITICAL: ['fonts', 'critical-css', 'above-fold-images'] as const,
  
  // Important assets - load with high priority
  HIGH_PRIORITY: ['navigation-icons', 'user-avatar', 'logo'] as const,
  
  // Normal assets - load when needed
  NORMAL: ['content-images', 'charts', 'reports'] as const,
  
  // Low priority assets - load when idle
  LOW_PRIORITY: ['background-images', 'decorative-icons'] as const,
  
  // Lazy load assets - load when visible
  LAZY: ['gallery-images', 'large-documents', 'videos'] as const
}

// Modern image format detection
export const supportsWebP = (): Promise<boolean> => {
  return new Promise((resolve) => {
    const webP = new Image()
    webP.onload = webP.onerror = () => {
      resolve(webP.height === 2)
    }
    webP.src = 'data:image/webp;base64,UklGRjoAAABXRUJQVlA4IC4AAACyAgCdASoCAAIALmk0mk0iIiIiIgBoSygABc6WWgAA/veff/0PP8bA//LwYAAA'
  })
}

export const supportsAVIF = (): Promise<boolean> => {
  return new Promise((resolve) => {
    const avif = new Image()
    avif.onload = avif.onerror = () => {
      resolve(avif.height === 2)
    }
    avif.src = 'data:image/avif;base64,AAAAIGZ0eXBhdmlmAAAAAGF2aWZtaWYxbWlhZk1BMUIAAADybWV0YQAAAAAAAAAoaGRscgAAAAAAAAAAcGljdAAAAAAAAAAAAAAAAGxpYmF2aWYAAAAADnBpdG0AAAAAAAEAAAAeaWxvYwAAAABEAAABAAEAAAABAAABGgAAAB0AAAAoaWluZgAAAAAAAQAAABppbmZlAgAAAAABAABhdjAxQ29sb3IAAAAAamlwcnAAAABLaXBjbwAAABRpc3BlAAAAAAAAAAIAAAACAAAAEHBpeGkAAAAAAwgICAAAAAxhdjFDgQ0MAAAAABNjb2xybmNseAACAAIAAYAAAAAXaXBtYQAAAAAAAAABAAEEAQKDBAAAACVtZGF0EgAKCBgABogQEAwgMg8f8D///8WfhwB8+ErK42A='
  })
}

// Responsive image source generation
export const generateResponsiveImageSources = (
  basePath: string,
  alt: string,
  sizes?: string
): {
  webp: string[]
  fallback: string[]
  sizes: string
} => {
  const breakpoints = Object.entries(IMAGE_OPTIMIZATION.BREAKPOINTS)
  
  const webpSources = breakpoints.map(([key, width]) => 
    `${basePath}-${key}.webp ${width}w`
  )
  
  const fallbackSources = breakpoints.map(([key, width]) => 
    `${basePath}-${key}.jpg ${width}w`
  )
  
  const defaultSizes = sizes || '(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw'
  
  return {
    webp: webpSources,
    fallback: fallbackSources,
    sizes: defaultSizes
  }
}

// Lazy loading intersection observer
export const createLazyLoadObserver = (
  callback: (entry: IntersectionObserverEntry) => void,
  options?: IntersectionObserverInit
) => {
  const defaultOptions: IntersectionObserverInit = {
    root: null,
    rootMargin: '50px',
    threshold: 0.1,
    ...options
  }
  
  return new IntersectionObserver((entries) => {
    entries.forEach(callback)
  }, defaultOptions)
}

// Asset preloading utilities
export const preloadImage = (src: string): Promise<void> => {
  return new Promise((resolve, reject) => {
    const img = new Image()
    img.onload = () => resolve()
    img.onerror = reject
    img.src = src
  })
}

export const preloadFont = (fontUrl: string, fontDisplay: string = 'swap'): void => {
  const link = document.createElement('link')
  link.rel = 'preload'
  link.as = 'font'
  link.type = 'font/woff2'
  link.crossOrigin = 'anonymous'
  link.href = fontUrl
  
  // Add font-display property
  const style = document.createElement('style')
  style.textContent = `
    @font-face {
      font-family: 'PreloadedFont';
      src: url('${fontUrl}') format('woff2');
      font-display: ${fontDisplay};
    }
  `
  
  document.head.appendChild(link)
  document.head.appendChild(style)
}

export const preloadCriticalAssets = async (): Promise<void> => {
  // Preload critical fonts
  FONT_OPTIMIZATION.PRELOAD_FONTS.forEach(fontUrl => {
    preloadFont(fontUrl, FONT_OPTIMIZATION.DISPLAY.swap)
  })
  
  // Preload critical images (if any)
  const criticalImages = [
    '/logo.svg',
    '/favicon.ico'
  ]
  
  const imagePromises = criticalImages.map(src => 
    preloadImage(src).catch(() => {
      // Silently fail for missing images
      console.warn(`Failed to preload image: ${src}`)
    })
  )
  
  await Promise.allSettled(imagePromises)
}

// Asset compression utilities
export const compressImage = (
  file: File,
  quality: number = IMAGE_OPTIMIZATION.QUALITY.preview,
  maxWidth?: number
): Promise<Blob> => {
  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    const img = new Image()
    
    img.onload = () => {
      // Calculate dimensions
      let { width, height } = img
      
      if (maxWidth && width > maxWidth) {
        height = (height * maxWidth) / width
        width = maxWidth
      }
      
      canvas.width = width
      canvas.height = height
      
      // Draw and compress
      ctx?.drawImage(img, 0, 0, width, height)
      
      canvas.toBlob(
        (blob) => {
          if (blob) {
            resolve(blob)
          } else {
            reject(new Error('Failed to compress image'))
          }
        },
        'image/jpeg',
        quality / 100
      )
    }
    
    img.onerror = reject
    img.src = URL.createObjectURL(file)
  })
}

// Asset caching utilities
export const getCachedAsset = (key: string): string | null => {
  try {
    return localStorage.getItem(`asset_cache_${key}`)
  } catch {
    return null
  }
}

export const setCachedAsset = (key: string, data: string): void => {
  try {
    localStorage.setItem(`asset_cache_${key}`, data)
  } catch {
    // Storage quota exceeded or disabled
    console.warn('Failed to cache asset:', key)
  }
}

// Performance monitoring for assets
export const trackAssetPerformance = (assetUrl: string, startTime: number): void => {
  const loadTime = performance.now() - startTime
  
  // Log performance metrics
  if (process.env.NODE_ENV === 'development') {
    console.log(`Asset loaded: ${assetUrl} (${loadTime.toFixed(2)}ms)`)
  }
  
  // Send to analytics if configured
  if (typeof window !== 'undefined' && 'gtag' in window) {
    (window as any).gtag('event', 'asset_load_time', {
      event_category: 'Performance',
      event_label: assetUrl,
      value: Math.round(loadTime)
    })
  }
}

// Asset optimization React hooks
export const useImageOptimization = () => {
  const [supportsModernFormats, setSupportsModernFormats] = React.useState({
    webp: false,
    avif: false
  })
  
  React.useEffect(() => {
    Promise.all([
      supportsWebP(),
      supportsAVIF()
    ]).then(([webp, avif]) => {
      setSupportsModernFormats({ webp, avif })
    })
  }, [])
  
  const getOptimalImageSrc = React.useCallback((basePath: string, fallback: string) => {
    if (supportsModernFormats.avif) {
      return `${basePath}.avif`
    }
    if (supportsModernFormats.webp) {
      return `${basePath}.webp`
    }
    return fallback
  }, [supportsModernFormats])
  
  return { supportsModernFormats, getOptimalImageSrc }
}

// Initialize asset optimization
export const initializeAssetOptimization = (): void => {
  // Preload critical assets
  preloadCriticalAssets()
  
  // Set up service worker for asset caching (if available)
  if ('serviceWorker' in navigator) {
    navigator.serviceWorker.register('/sw.js').catch(() => {
      // Service worker registration failed
      console.warn('Service worker registration failed')
    })
  }
  
  // Enable resource hints
  const resourceHints = [
    { rel: 'dns-prefetch', href: '//fonts.googleapis.com' },
    { rel: 'dns-prefetch', href: '//fonts.gstatic.com' },
    { rel: 'preconnect', href: '//api.example.com' }
  ]
  
  resourceHints.forEach(hint => {
    const link = document.createElement('link')
    link.rel = hint.rel
    link.href = hint.href
    if (hint.rel === 'preconnect') {
      link.crossOrigin = 'anonymous'
    }
    document.head.appendChild(link)
  })
}
