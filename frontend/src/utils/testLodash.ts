/**
 * Test file to verify lodash module resolution
 * This file tests different ways of importing lodash to ensure compatibility
 */

// Test different import patterns that might be used by dependencies
import get from 'lodash/get'
import set from 'lodash/set'
import merge from 'lodash/merge'
import debounce from 'lodash/debounce'

// Test object to verify imports work
const testObject = {
  user: {
    name: '<PERSON>',
    profile: {
      email: '<EMAIL>'
    }
  }
}

// Test lodash functions
export const testLodashImports = () => {
  console.log('Testing lodash imports...')
  
  // Test get
  const userName = get(testObject, 'user.name', 'Unknown')
  console.log('get test:', userName)
  
  // Test set
  const newObject = { ...testObject }
  set(newObject, 'user.profile.phone', '+1234567890')
  console.log('set test:', get(newObject, 'user.profile.phone'))
  
  // Test merge
  const merged = merge({}, testObject, { user: { age: 30 } })
  console.log('merge test:', merged)
  
  // Test debounce
  const debouncedFunction = debounce(() => {
    console.log('debounce test: function called')
  }, 100)
  
  debouncedFunction()
  
  return {
    get: !!get,
    set: !!set,
    merge: !!merge,
    debounce: !!debounce
  }
}

// Export for use in components if needed
export { get, set, merge, debounce }
