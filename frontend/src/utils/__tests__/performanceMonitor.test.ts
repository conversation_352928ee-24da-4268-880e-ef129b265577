/**
 * Performance Monitor Unit Tests
 * Comprehensive testing for performance monitoring and Core Web Vitals tracking
 */

import { 
  getPerformanceMonitor, 
  usePerformanceMonitoring, 
  PERFORMANCE_BUDGETS 
} from '../performanceMonitor'
import { renderHook } from '@testing-library/react'

// Mock performance API
const mockPerformance = {
  timeOrigin: 1000,
  getEntriesByType: jest.fn(),
  now: jest.fn(() => 2000),
  memory: {
    usedJSHeapSize: 1024 * 1024,
    totalJSHeapSize: 2 * 1024 * 1024,
    jsHeapSizeLimit: 4 * 1024 * 1024
  }
}

const mockNavigator = {
  userAgent: 'Mozilla/5.0 (Test Browser)',
  connection: {
    effectiveType: '4g',
    downlink: 10,
    type: 'wifi'
  }
}

// Mock PerformanceObserver
class MockPerformanceObserver {
  private callback: (list: any) => void
  
  constructor(callback: (list: any) => void) {
    this.callback = callback
  }
  
  observe() {
    // Mock implementation
  }
  
  disconnect() {
    // Mock implementation
  }
  
  // Helper method to trigger callback for testing
  triggerCallback(entries: any[]) {
    this.callback({
      getEntries: () => entries
    })
  }
}

// Setup global mocks
Object.defineProperty(global, 'performance', {
  value: mockPerformance,
  writable: true
})

Object.defineProperty(global, 'navigator', {
  value: mockNavigator,
  writable: true
})

Object.defineProperty(global, 'PerformanceObserver', {
  value: MockPerformanceObserver,
  writable: true
})

Object.defineProperty(global, 'window', {
  value: {
    innerWidth: 1920,
    innerHeight: 1080
  },
  writable: true
})

Object.defineProperty(global, 'document', {
  value: {
    readyState: 'complete',
    addEventListener: jest.fn(),
    visibilityState: 'visible'
  },
  writable: true
})

describe('Performance Monitor', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    mockPerformance.getEntriesByType.mockReturnValue([])
  })

  describe('Initialization', () => {
    test('should create singleton instance', () => {
      const monitor1 = getPerformanceMonitor()
      const monitor2 = getPerformanceMonitor()
      
      expect(monitor1).toBe(monitor2)
    })

    test('should collect basic metrics on initialization', () => {
      // Mock navigation timing
      mockPerformance.getEntriesByType.mockReturnValue([{
        navigationStart: 1000,
        domContentLoadedEventEnd: 1500,
        loadEventEnd: 2000,
        responseStart: 1200,
        requestStart: 1100
      }])

      const monitor = getPerformanceMonitor()
      const metrics = monitor.getMetrics()

      expect(metrics.navigationStart).toBeDefined()
      expect(metrics.domContentLoaded).toBeDefined()
      expect(metrics.loadComplete).toBeDefined()
      expect(metrics.ttfb).toBeDefined()
    })
  })

  describe('Metrics Collection', () => {
    test('should collect memory information', () => {
      const monitor = getPerformanceMonitor()
      const metrics = monitor.getMetrics()

      expect(metrics.usedJSHeapSize).toBe(1024 * 1024)
      expect(metrics.totalJSHeapSize).toBe(2 * 1024 * 1024)
      expect(metrics.jsHeapSizeLimit).toBe(4 * 1024 * 1024)
    })

    test('should collect network information', () => {
      const monitor = getPerformanceMonitor()
      const metrics = monitor.getMetrics()

      expect(metrics.connectionType).toBe('wifi')
      expect(metrics.effectiveType).toBe('4g')
      expect(metrics.downlink).toBe(10)
    })

    test('should collect viewport information', () => {
      const monitor = getPerformanceMonitor()
      const metrics = monitor.getMetrics()

      expect(metrics.viewport).toEqual({
        width: 1920,
        height: 1080
      })
      expect(metrics.userAgent).toBe('Mozilla/5.0 (Test Browser)')
    })

    test('should handle missing performance APIs gracefully', () => {
      // Mock missing memory API
      const originalMemory = (performance as any).memory
      delete (performance as any).memory

      const monitor = getPerformanceMonitor()
      const metrics = monitor.getMetrics()

      expect(metrics.usedJSHeapSize).toBeUndefined()
      expect(metrics.totalJSHeapSize).toBeUndefined()

      // Restore
      ;(performance as any).memory = originalMemory
    })
  })

  describe('Performance Scoring', () => {
    test('should calculate performance score correctly', () => {
      const monitor = getPerformanceMonitor()
      
      // Mock good performance metrics
      const mockMetrics = {
        lcp: 2000, // Good (< 2500ms)
        fid: 50,   // Good (< 100ms)
        cls: 0.05, // Good (< 0.1)
        fcp: 1500  // Good (< 1800ms)
      }

      // Manually set metrics for testing
      Object.assign(monitor.getMetrics(), mockMetrics)

      const score = monitor.getPerformanceScore()

      expect(score.score).toBe(100) // All metrics are good
      expect(score.details.lcp.status).toBe('good')
      expect(score.details.fid.status).toBe('good')
      expect(score.details.cls.status).toBe('good')
    })

    test('should handle poor performance metrics', () => {
      const monitor = getPerformanceMonitor()
      
      // Mock poor performance metrics
      const mockMetrics = {
        lcp: 5000, // Poor (> 4000ms)
        fid: 500,  // Poor (> 300ms)
        cls: 0.5,  // Poor (> 0.25)
        fcp: 3000  // Poor (> 1800ms)
      }

      Object.assign(monitor.getMetrics(), mockMetrics)

      const score = monitor.getPerformanceScore()

      expect(score.score).toBe(0) // All metrics are poor
      expect(score.details.lcp.status).toBe('poor')
      expect(score.details.fid.status).toBe('poor')
      expect(score.details.cls.status).toBe('poor')
    })

    test('should handle mixed performance metrics', () => {
      const monitor = getPerformanceMonitor()
      
      // Mock mixed performance metrics
      const mockMetrics = {
        lcp: 3000, // Needs improvement (2500-4000ms)
        fid: 50,   // Good (< 100ms)
        cls: 0.05, // Good (< 0.1)
        fcp: 1500  // Good (< 1800ms)
      }

      Object.assign(monitor.getMetrics(), mockMetrics)

      const score = monitor.getPerformanceScore()

      expect(score.score).toBe(75) // 3 good (100) + 1 needs improvement (50) / 4
      expect(score.details.lcp.status).toBe('needs-improvement')
      expect(score.details.fid.status).toBe('good')
    })
  })

  describe('Performance Budgets', () => {
    test('should have correct budget thresholds', () => {
      expect(PERFORMANCE_BUDGETS.LCP_GOOD).toBe(2500)
      expect(PERFORMANCE_BUDGETS.LCP_NEEDS_IMPROVEMENT).toBe(4000)
      expect(PERFORMANCE_BUDGETS.FID_GOOD).toBe(100)
      expect(PERFORMANCE_BUDGETS.FID_NEEDS_IMPROVEMENT).toBe(300)
      expect(PERFORMANCE_BUDGETS.CLS_GOOD).toBe(0.1)
      expect(PERFORMANCE_BUDGETS.CLS_NEEDS_IMPROVEMENT).toBe(0.25)
    })
  })

  describe('Logging and Analytics', () => {
    test('should log performance report to console', () => {
      const consoleSpy = jest.spyOn(console, 'group').mockImplementation()
      const consoleLogSpy = jest.spyOn(console, 'log').mockImplementation()
      const consoleGroupEndSpy = jest.spyOn(console, 'groupEnd').mockImplementation()

      const monitor = getPerformanceMonitor()
      monitor.logPerformanceReport()

      expect(consoleSpy).toHaveBeenCalledWith('🚀 Performance Report')
      expect(consoleLogSpy).toHaveBeenCalled()
      expect(consoleGroupEndSpy).toHaveBeenCalled()

      consoleSpy.mockRestore()
      consoleLogSpy.mockRestore()
      consoleGroupEndSpy.mockRestore()
    })

    test('should send metrics to analytics endpoint', async () => {
      global.fetch = jest.fn().mockResolvedValue({
        ok: true
      })

      const monitor = getPerformanceMonitor()
      monitor.sendMetricsToAnalytics('https://analytics.example.com/metrics')

      expect(global.fetch).toHaveBeenCalledWith(
        'https://analytics.example.com/metrics',
        expect.objectContaining({
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: expect.stringContaining('"url"')
        })
      )
    })

    test('should handle analytics errors gracefully', async () => {
      global.fetch = jest.fn().mockRejectedValue(new Error('Network error'))
      const consoleWarnSpy = jest.spyOn(console, 'warn').mockImplementation()

      const monitor = getPerformanceMonitor()
      monitor.sendMetricsToAnalytics('https://analytics.example.com/metrics')

      // Wait for promise to resolve
      await new Promise(resolve => setTimeout(resolve, 0))

      expect(consoleWarnSpy).toHaveBeenCalledWith(
        'Failed to send performance metrics:',
        expect.any(Error)
      )

      consoleWarnSpy.mockRestore()
    })
  })

  describe('Cleanup', () => {
    test('should cleanup observers on destroy', () => {
      const monitor = getPerformanceMonitor()
      
      // Mock observers
      const mockObserver = {
        disconnect: jest.fn()
      }
      ;(monitor as any).observers = [mockObserver]

      monitor.cleanup()

      expect(mockObserver.disconnect).toHaveBeenCalled()
    })
  })
})

describe('usePerformanceMonitoring Hook', () => {
  test('should provide performance monitoring methods', () => {
    const { result } = renderHook(() => usePerformanceMonitoring())

    expect(result.current.getMetrics).toBeInstanceOf(Function)
    expect(result.current.getScore).toBeInstanceOf(Function)
    expect(result.current.logReport).toBeInstanceOf(Function)
    expect(result.current.sendToAnalytics).toBeInstanceOf(Function)
  })

  test('should return current metrics', () => {
    const { result } = renderHook(() => usePerformanceMonitoring())
    
    const metrics = result.current.getMetrics()
    
    expect(metrics).toHaveProperty('timestamp')
    expect(metrics.timestamp).toBeGreaterThan(0)
  })

  test('should return performance score', () => {
    const { result } = renderHook(() => usePerformanceMonitoring())
    
    const score = result.current.getScore()
    
    expect(score).toHaveProperty('score')
    expect(score).toHaveProperty('details')
    expect(typeof score.score).toBe('number')
  })
})
