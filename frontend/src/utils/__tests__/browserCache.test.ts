/**
 * Browser Cache Unit Tests
 * Comprehensive testing for browser caching utilities
 */

import <PERSON>rowser<PERSON><PERSON>, { 
  memoryCache, 
  sessionCache, 
  persistentCache,
  CacheStrategies,
  CacheManager
} from '../browserCache'

// Mock localStorage and sessionStorage
const createMockStorage = () => {
  let store: Record<string, string> = {}
  
  return {
    getItem: jest.fn((key: string) => store[key] || null),
    setItem: jest.fn((key: string, value: string) => {
      store[key] = value
    }),
    removeItem: jest.fn((key: string) => {
      delete store[key]
    }),
    clear: jest.fn(() => {
      store = {}
    }),
    get length() {
      return Object.keys(store).length
    },
    key: jest.fn((index: number) => Object.keys(store)[index] || null)
  }
}

Object.defineProperty(global, 'localStorage', {
  value: createMockStorage(),
  writable: true
})

Object.defineProperty(global, 'sessionStorage', {
  value: createMockStorage(),
  writable: true
})

describe('BrowserCache', () => {
  let cache: BrowserCache

  beforeEach(() => {
    cache = new BrowserCache({
      storageType: 'memory',
      defaultTTL: 1000, // 1 second for testing
      maxSize: 5
    })
    localStorage.clear()
    sessionStorage.clear()
  })

  describe('Memory Cache', () => {
    test('should set and get entries', async () => {
      const testData = { id: 1, name: 'Test' }
      
      await cache.set('test-key', testData)
      const result = await cache.get('test-key')
      
      expect(result).toEqual(testData)
    })

    test('should return null for non-existent entries', async () => {
      const result = await cache.get('non-existent')
      expect(result).toBeNull()
    })

    test('should handle TTL expiration', async () => {
      const testData = { id: 1, name: 'Test' }
      
      await cache.set('test-key', testData, 100) // 100ms TTL
      
      // Should exist immediately
      expect(await cache.get('test-key')).toEqual(testData)
      
      // Mock time passage
      jest.spyOn(Date, 'now').mockReturnValue(Date.now() + 200)
      
      // Should be expired
      expect(await cache.get('test-key')).toBeNull()
      
      jest.restoreAllMocks()
    })

    test('should check if key exists', async () => {
      await cache.set('test-key', { id: 1 })
      
      expect(await cache.has('test-key')).toBe(true)
      expect(await cache.has('non-existent')).toBe(false)
    })

    test('should remove entries', async () => {
      await cache.set('test-key', { id: 1 })
      
      expect(await cache.has('test-key')).toBe(true)
      
      await cache.remove('test-key')
      
      expect(await cache.has('test-key')).toBe(false)
    })

    test('should clear all entries', async () => {
      await cache.set('key1', { id: 1 })
      await cache.set('key2', { id: 2 })
      
      expect(await cache.size()).toBe(2)
      
      await cache.clear()
      
      expect(await cache.size()).toBe(0)
    })

    test('should get cache size', async () => {
      expect(await cache.size()).toBe(0)
      
      await cache.set('key1', { id: 1 })
      await cache.set('key2', { id: 2 })
      
      expect(await cache.size()).toBe(2)
    })

    test('should get all keys', async () => {
      await cache.set('key1', { id: 1 })
      await cache.set('key2', { id: 2 })
      
      const keys = await cache.keys()
      
      expect(keys).toContain('key1')
      expect(keys).toContain('key2')
      expect(keys).toHaveLength(2)
    })
  })

  describe('LocalStorage Cache', () => {
    beforeEach(() => {
      cache = new BrowserCache({
        storageType: 'localStorage',
        defaultTTL: 1000,
        keyPrefix: 'test_'
      })
    })

    test('should store data in localStorage', async () => {
      const testData = { id: 1, name: 'Test' }
      
      await cache.set('test-key', testData)
      
      expect(localStorage.setItem).toHaveBeenCalledWith(
        'test_test-key',
        expect.stringContaining('"data"')
      )
    })

    test('should retrieve data from localStorage', async () => {
      const testData = { id: 1, name: 'Test' }
      
      // Mock localStorage to return our test data
      const mockEntry = {
        data: testData,
        timestamp: Date.now(),
        ttl: 1000,
        version: '1.0.0'
      }
      ;(localStorage.getItem as jest.Mock).mockReturnValue(JSON.stringify(mockEntry))
      
      const result = await cache.get('test-key')
      
      expect(result).toEqual(testData)
      expect(localStorage.getItem).toHaveBeenCalledWith('test_test-key')
    })

    test('should handle localStorage errors gracefully', async () => {
      // Mock localStorage to throw error
      ;(localStorage.setItem as jest.Mock).mockImplementation(() => {
        throw new Error('Storage quota exceeded')
      })
      
      const result = await cache.set('test-key', { data: 'test' })
      
      expect(result).toBe(false)
    })
  })

  describe('SessionStorage Cache', () => {
    beforeEach(() => {
      cache = new BrowserCache({
        storageType: 'sessionStorage',
        defaultTTL: 1000,
        keyPrefix: 'sess_'
      })
    })

    test('should store data in sessionStorage', async () => {
      const testData = { id: 1, name: 'Test' }
      
      await cache.set('test-key', testData)
      
      expect(sessionStorage.setItem).toHaveBeenCalledWith(
        'sess_test-key',
        expect.stringContaining('"data"')
      )
    })
  })

  describe('Cache Cleanup', () => {
    test('should cleanup expired entries', async () => {
      const cache = new BrowserCache({
        storageType: 'memory',
        defaultTTL: 100 // 100ms
      })
      
      await cache.set('key1', { id: 1 }, 50)  // Expires quickly
      await cache.set('key2', { id: 2 }, 1000) // Expires later
      
      // Mock time passage
      jest.spyOn(Date, 'now').mockReturnValue(Date.now() + 100)
      
      const cleanedCount = await cache.cleanup()
      
      expect(cleanedCount).toBe(1)
      expect(await cache.get('key1')).toBeNull()
      expect(await cache.get('key2')).toEqual({ id: 2 })
      
      jest.restoreAllMocks()
    })
  })

  describe('Compression', () => {
    test('should handle compression when enabled', async () => {
      const cache = new BrowserCache({
        storageType: 'memory',
        compression: true
      })
      
      const testData = { id: 1, name: 'Test Data' }
      
      await cache.set('test-key', testData)
      const result = await cache.get('test-key')
      
      expect(result).toEqual(testData)
    })
  })
})

describe('Pre-configured Cache Instances', () => {
  beforeEach(() => {
    memoryCache.clear()
    sessionCache.clear()
    persistentCache.clear()
  })

  test('memoryCache should work correctly', async () => {
    await memoryCache.set('test', { data: 'memory' })
    const result = await memoryCache.get('test')
    
    expect(result).toEqual({ data: 'memory' })
  })

  test('sessionCache should work correctly', async () => {
    await sessionCache.set('test', { data: 'session' })
    const result = await sessionCache.get('test')
    
    expect(result).toEqual({ data: 'session' })
  })

  test('persistentCache should work correctly', async () => {
    await persistentCache.set('test', { data: 'persistent' })
    const result = await persistentCache.get('test')
    
    expect(result).toEqual({ data: 'persistent' })
  })
})

describe('Cache Strategies', () => {
  beforeEach(() => {
    memoryCache.clear()
  })

  describe('cacheFirst', () => {
    test('should return cached data if available', async () => {
      const cachedData = { id: 1, cached: true }
      const fetchedData = { id: 1, cached: false }
      
      await memoryCache.set('test-key', cachedData)
      
      const fetcher = jest.fn().mockResolvedValue(fetchedData)
      
      const result = await CacheStrategies.cacheFirst('test-key', fetcher)
      
      expect(result).toEqual(cachedData)
      expect(fetcher).not.toHaveBeenCalled()
    })

    test('should fetch and cache if not in cache', async () => {
      const fetchedData = { id: 1, fetched: true }
      const fetcher = jest.fn().mockResolvedValue(fetchedData)
      
      const result = await CacheStrategies.cacheFirst('test-key', fetcher)
      
      expect(result).toEqual(fetchedData)
      expect(fetcher).toHaveBeenCalled()
      
      // Should be cached now
      const cached = await memoryCache.get('test-key')
      expect(cached).toEqual(fetchedData)
    })
  })

  describe('networkFirst', () => {
    test('should fetch from network first', async () => {
      const cachedData = { id: 1, cached: true }
      const fetchedData = { id: 1, fetched: true }
      
      await memoryCache.set('test-key', cachedData)
      
      const fetcher = jest.fn().mockResolvedValue(fetchedData)
      
      const result = await CacheStrategies.networkFirst('test-key', fetcher)
      
      expect(result).toEqual(fetchedData)
      expect(fetcher).toHaveBeenCalled()
    })

    test('should fallback to cache on network error', async () => {
      const cachedData = { id: 1, cached: true }
      
      await memoryCache.set('test-key', cachedData)
      
      const fetcher = jest.fn().mockRejectedValue(new Error('Network error'))
      
      const result = await CacheStrategies.networkFirst('test-key', fetcher)
      
      expect(result).toEqual(cachedData)
      expect(fetcher).toHaveBeenCalled()
    })

    test('should throw error if network fails and no cache', async () => {
      const fetcher = jest.fn().mockRejectedValue(new Error('Network error'))
      
      await expect(
        CacheStrategies.networkFirst('test-key', fetcher)
      ).rejects.toThrow('Network error')
    })
  })

  describe('staleWhileRevalidate', () => {
    test('should return cached data immediately and update in background', async () => {
      const cachedData = { id: 1, cached: true }
      const fetchedData = { id: 1, fetched: true }
      
      await memoryCache.set('test-key', cachedData)
      
      const fetcher = jest.fn().mockResolvedValue(fetchedData)
      
      const result = await CacheStrategies.staleWhileRevalidate('test-key', fetcher)
      
      expect(result).toEqual(cachedData)
      expect(fetcher).toHaveBeenCalled()
      
      // Wait for background update
      await new Promise(resolve => setTimeout(resolve, 0))
      
      // Cache should be updated
      const updated = await memoryCache.get('test-key')
      expect(updated).toEqual(fetchedData)
    })

    test('should wait for network if no cache', async () => {
      const fetchedData = { id: 1, fetched: true }
      const fetcher = jest.fn().mockResolvedValue(fetchedData)
      
      const result = await CacheStrategies.staleWhileRevalidate('test-key', fetcher)
      
      expect(result).toEqual(fetchedData)
      expect(fetcher).toHaveBeenCalled()
    })
  })
})

describe('Cache Manager', () => {
  test('should initialize cache system', async () => {
    const cleanupSpy = jest.spyOn(memoryCache, 'cleanup').mockResolvedValue(0)
    
    await CacheManager.init()
    
    expect(cleanupSpy).toHaveBeenCalled()
    
    cleanupSpy.mockRestore()
  })

  test('should clear all caches', async () => {
    const memoryClearSpy = jest.spyOn(memoryCache, 'clear')
    const sessionClearSpy = jest.spyOn(sessionCache, 'clear')
    const persistentClearSpy = jest.spyOn(persistentCache, 'clear')
    
    await CacheManager.clearAll()
    
    expect(memoryClearSpy).toHaveBeenCalled()
    expect(sessionClearSpy).toHaveBeenCalled()
    expect(persistentClearSpy).toHaveBeenCalled()
    
    memoryClearSpy.mockRestore()
    sessionClearSpy.mockRestore()
    persistentClearSpy.mockRestore()
  })

  test('should get cache statistics', async () => {
    const memorySizeSpy = jest.spyOn(memoryCache, 'size').mockResolvedValue(5)
    const sessionSizeSpy = jest.spyOn(sessionCache, 'size').mockResolvedValue(3)
    const persistentSizeSpy = jest.spyOn(persistentCache, 'size').mockResolvedValue(10)
    
    const stats = await CacheManager.getStats()
    
    expect(stats).toEqual({
      memory: 5,
      session: 3,
      persistent: 10,
      total: 18
    })
    
    memorySizeSpy.mockRestore()
    sessionSizeSpy.mockRestore()
    persistentSizeSpy.mockRestore()
  })
})
