/**
 * Asset Optimization Unit Tests
 * Comprehensive testing for asset optimization utilities
 */

import {
  IMAGE_OPTIMIZATION,
  FONT_OPTIMIZATION,
  LOADING_STRATEGIES,
  supportsWebP,
  supportsAVIF,
  generateResponsiveImageSources,
  createLazyLoadObserver,
  preloadImage,
  preloadFont,
  compressImage,
  getCachedAsset,
  setCachedAsset,
  trackAssetPerformance,
  useImageOptimization,
  initializeAssetOptimization
} from '../assetOptimization'
import { renderHook } from '@testing-library/react'

// Mock DOM APIs
Object.defineProperty(global, 'Image', {
  value: class MockImage {
    onload: (() => void) | null = null
    onerror: (() => void) | null = null
    src: string = ''
    height: number = 0
    
    constructor() {
      // Simulate async loading
      setTimeout(() => {
        if (this.src.includes('webp') || this.src.includes('avif')) {
          this.height = 2 // Simulate format support
        }
        this.onload?.()
      }, 0)
    }
  }
})

Object.defineProperty(global, 'localStorage', {
  value: {
    getItem: jest.fn(),
    setItem: jest.fn(),
    removeItem: jest.fn(),
    clear: jest.fn()
  }
})

Object.defineProperty(global, 'document', {
  value: {
    createElement: jest.fn(() => ({
      rel: '',
      as: '',
      type: '',
      crossOrigin: '',
      href: '',
      textContent: ''
    })),
    head: {
      appendChild: jest.fn()
    }
  }
})

Object.defineProperty(global, 'performance', {
  value: {
    now: jest.fn(() => 1000)
  }
})

Object.defineProperty(global, 'navigator', {
  value: {
    serviceWorker: {
      register: jest.fn().mockResolvedValue({})
    }
  }
})

// Mock IntersectionObserver
class MockIntersectionObserver {
  private callback: (entries: any[]) => void
  
  constructor(callback: (entries: any[]) => void) {
    this.callback = callback
  }
  
  observe() {}
  disconnect() {}
  
  // Helper method for testing
  triggerCallback(entries: any[]) {
    this.callback(entries)
  }
}

Object.defineProperty(global, 'IntersectionObserver', {
  value: MockIntersectionObserver
})

describe('Asset Optimization Constants', () => {
  test('should have correct image optimization settings', () => {
    expect(IMAGE_OPTIMIZATION.FORMATS).toEqual(['webp', 'avif', 'png', 'jpg', 'jpeg'])
    expect(IMAGE_OPTIMIZATION.QUALITY.thumbnail).toBe(60)
    expect(IMAGE_OPTIMIZATION.QUALITY.preview).toBe(75)
    expect(IMAGE_OPTIMIZATION.QUALITY.full).toBe(85)
    expect(IMAGE_OPTIMIZATION.BREAKPOINTS.sm).toBe(640)
    expect(IMAGE_OPTIMIZATION.BREAKPOINTS.lg).toBe(1024)
  })

  test('should have correct font optimization settings', () => {
    expect(FONT_OPTIMIZATION.DISPLAY.swap).toBe('swap')
    expect(FONT_OPTIMIZATION.FORMATS).toEqual(['woff2', 'woff', 'ttf'])
    expect(FONT_OPTIMIZATION.PRELOAD_FONTS).toContain('/fonts/inter-var.woff2')
  })

  test('should have correct loading strategies', () => {
    expect(LOADING_STRATEGIES.CRITICAL).toContain('fonts')
    expect(LOADING_STRATEGIES.HIGH_PRIORITY).toContain('navigation-icons')
    expect(LOADING_STRATEGIES.LAZY).toContain('gallery-images')
  })
})

describe('Format Support Detection', () => {
  test('supportsWebP should detect WebP support', async () => {
    const result = await supportsWebP()
    expect(typeof result).toBe('boolean')
  })

  test('supportsAVIF should detect AVIF support', async () => {
    const result = await supportsAVIF()
    expect(typeof result).toBe('boolean')
  })
})

describe('Responsive Image Sources', () => {
  test('should generate responsive image sources', () => {
    const result = generateResponsiveImageSources('/images/hero', 'Hero image')
    
    expect(result.webp).toContain('/images/hero-sm.webp 640w')
    expect(result.webp).toContain('/images/hero-lg.webp 1024w')
    expect(result.fallback).toContain('/images/hero-sm.jpg 640w')
    expect(result.fallback).toContain('/images/hero-lg.jpg 1024w')
    expect(result.sizes).toBe('(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw')
  })

  test('should use custom sizes', () => {
    const customSizes = '(max-width: 768px) 100vw, 50vw'
    const result = generateResponsiveImageSources('/images/hero', 'Hero image', customSizes)
    
    expect(result.sizes).toBe(customSizes)
  })
})

describe('Lazy Loading Observer', () => {
  test('should create intersection observer', () => {
    const callback = jest.fn()
    const observer = createLazyLoadObserver(callback)
    
    expect(observer).toBeInstanceOf(MockIntersectionObserver)
  })

  test('should use custom options', () => {
    const callback = jest.fn()
    const options = { rootMargin: '100px', threshold: 0.5 }
    
    const observer = createLazyLoadObserver(callback, options)
    expect(observer).toBeInstanceOf(MockIntersectionObserver)
  })
})

describe('Asset Preloading', () => {
  test('preloadImage should load image', async () => {
    const promise = preloadImage('/test-image.jpg')
    
    await expect(promise).resolves.toBeUndefined()
  })

  test('preloadImage should handle errors', async () => {
    // Mock image that fails to load
    Object.defineProperty(global, 'Image', {
      value: class MockImage {
        onload: (() => void) | null = null
        onerror: (() => void) | null = null
        src: string = ''
        
        constructor() {
          setTimeout(() => {
            this.onerror?.()
          }, 0)
        }
      }
    })

    const promise = preloadImage('/invalid-image.jpg')
    
    await expect(promise).rejects.toBeUndefined()
  })

  test('preloadFont should create link and style elements', () => {
    const createElementSpy = jest.spyOn(document, 'createElement')
    const appendChildSpy = jest.spyOn(document.head, 'appendChild')
    
    preloadFont('/fonts/test.woff2', 'swap')
    
    expect(createElementSpy).toHaveBeenCalledWith('link')
    expect(createElementSpy).toHaveBeenCalledWith('style')
    expect(appendChildSpy).toHaveBeenCalledTimes(2)
    
    createElementSpy.mockRestore()
    appendChildSpy.mockRestore()
  })
})

describe('Image Compression', () => {
  // Mock canvas and context
  const mockCanvas = {
    width: 0,
    height: 0,
    getContext: jest.fn(() => ({
      drawImage: jest.fn()
    })),
    toBlob: jest.fn((callback) => {
      callback(new Blob(['compressed'], { type: 'image/jpeg' }))
    })
  }

  beforeEach(() => {
    Object.defineProperty(global, 'document', {
      value: {
        ...document,
        createElement: jest.fn(() => mockCanvas)
      }
    })

    Object.defineProperty(global, 'URL', {
      value: {
        createObjectURL: jest.fn(() => 'blob:test')
      }
    })
  })

  test('should compress image file', async () => {
    const mockFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' })
    
    const result = await compressImage(mockFile, 80)
    
    expect(result).toBeInstanceOf(Blob)
    expect(mockCanvas.toBlob).toHaveBeenCalled()
  })

  test('should handle compression with max width', async () => {
    const mockFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' })
    
    // Mock image with large dimensions
    Object.defineProperty(global, 'Image', {
      value: class MockImage {
        onload: (() => void) | null = null
        src: string = ''
        width: number = 2000
        height: number = 1000
        
        constructor() {
          setTimeout(() => {
            this.onload?.()
          }, 0)
        }
      }
    })

    const result = await compressImage(mockFile, 80, 1000)
    
    expect(result).toBeInstanceOf(Blob)
    expect(mockCanvas.width).toBe(1000)
    expect(mockCanvas.height).toBe(500) // Proportionally scaled
  })
})

describe('Asset Caching', () => {
  beforeEach(() => {
    ;(localStorage.getItem as jest.Mock).mockClear()
    ;(localStorage.setItem as jest.Mock).mockClear()
  })

  test('getCachedAsset should retrieve from localStorage', () => {
    ;(localStorage.getItem as jest.Mock).mockReturnValue('cached-data')
    
    const result = getCachedAsset('test-key')
    
    expect(result).toBe('cached-data')
    expect(localStorage.getItem).toHaveBeenCalledWith('asset_cache_test-key')
  })

  test('getCachedAsset should handle localStorage errors', () => {
    ;(localStorage.getItem as jest.Mock).mockImplementation(() => {
      throw new Error('Storage error')
    })
    
    const result = getCachedAsset('test-key')
    
    expect(result).toBeNull()
  })

  test('setCachedAsset should store in localStorage', () => {
    setCachedAsset('test-key', 'test-data')
    
    expect(localStorage.setItem).toHaveBeenCalledWith('asset_cache_test-key', 'test-data')
  })

  test('setCachedAsset should handle localStorage errors', () => {
    ;(localStorage.setItem as jest.Mock).mockImplementation(() => {
      throw new Error('Storage quota exceeded')
    })
    
    const consoleSpy = jest.spyOn(console, 'warn').mockImplementation()
    
    setCachedAsset('test-key', 'test-data')
    
    expect(consoleSpy).toHaveBeenCalledWith('Failed to cache asset:', 'test-key')
    
    consoleSpy.mockRestore()
  })
})

describe('Performance Tracking', () => {
  test('should track asset performance', () => {
    const consoleSpy = jest.spyOn(console, 'log').mockImplementation()
    
    // Mock development environment
    const originalEnv = process.env.NODE_ENV
    process.env.NODE_ENV = 'development'
    
    trackAssetPerformance('/test-asset.jpg', 500)
    
    expect(consoleSpy).toHaveBeenCalledWith(
      expect.stringContaining('Asset loaded: /test-asset.jpg')
    )
    
    process.env.NODE_ENV = originalEnv
    consoleSpy.mockRestore()
  })

  test('should send to analytics if gtag is available', () => {
    const mockGtag = jest.fn()
    ;(global as any).window = { gtag: mockGtag }
    
    trackAssetPerformance('/test-asset.jpg', 500)
    
    expect(mockGtag).toHaveBeenCalledWith('event', 'asset_load_time', {
      event_category: 'Performance',
      event_label: '/test-asset.jpg',
      value: 500
    })
    
    delete (global as any).window
  })
})

describe('useImageOptimization Hook', () => {
  test('should provide image optimization utilities', () => {
    const { result } = renderHook(() => useImageOptimization())
    
    expect(result.current.supportsModernFormats).toEqual({
      webp: false,
      avif: false
    })
    expect(result.current.getOptimalImageSrc).toBeInstanceOf(Function)
  })

  test('should return optimal image source', () => {
    const { result } = renderHook(() => useImageOptimization())
    
    const src = result.current.getOptimalImageSrc('/images/hero', '/images/hero.jpg')
    
    // Should return fallback since modern formats are not supported in test
    expect(src).toBe('/images/hero.jpg')
  })
})

describe('Asset Optimization Initialization', () => {
  test('should initialize asset optimization', () => {
    const createElementSpy = jest.spyOn(document, 'createElement')
    const appendChildSpy = jest.spyOn(document.head, 'appendChild')
    const registerSpy = jest.spyOn(navigator.serviceWorker, 'register')
    
    initializeAssetOptimization()
    
    expect(createElementSpy).toHaveBeenCalled()
    expect(appendChildSpy).toHaveBeenCalled()
    expect(registerSpy).toHaveBeenCalledWith('/sw.js')
    
    createElementSpy.mockRestore()
    appendChildSpy.mockRestore()
  })
})
