/**
 * API Cache Unit Tests
 * Comprehensive testing for API response caching system
 */

import APICache, { 
  userCache, 
  dashboardCache, 
  employeeCache, 
  cachedFetch,
  invalidateUserCache,
  invalidateEmployeeCache,
  persistentCache
} from '../apiCache'

// Mock fetch
global.fetch = jest.fn()

describe('APICache', () => {
  let cache: APICache

  beforeEach(() => {
    cache = new APICache({
      defaultTTL: 1000, // 1 second for testing
      maxEntries: 5
    })
    jest.clearAllMocks()
  })

  afterEach(() => {
    cache.destroy()
  })

  describe('Basic Operations', () => {
    test('should set and get cache entries', async () => {
      const testData = { id: 1, name: 'Test User' }
      
      await cache.set('/api/users/1', testData)
      const result = await cache.get('/api/users/1')
      
      expect(result).toEqual(testData)
    })

    test('should return null for non-existent entries', async () => {
      const result = await cache.get('/api/nonexistent')
      expect(result).toBeNull()
    })

    test('should handle cache expiration', async () => {
      const testData = { id: 1, name: 'Test User' }
      
      await cache.set('/api/users/1', testData, { ttl: 100 }) // 100ms TTL
      
      // Should exist immediately
      let result = await cache.get('/api/users/1')
      expect(result).toEqual(testData)
      
      // Wait for expiration
      await new Promise(resolve => setTimeout(resolve, 150))
      
      // Should be expired
      result = await cache.get('/api/users/1')
      expect(result).toBeNull()
    })

    test('should check if entry exists', () => {
      cache.set('/api/users/1', { id: 1 })
      
      expect(cache.has('/api/users/1')).toBe(true)
      expect(cache.has('/api/users/2')).toBe(false)
    })
  })

  describe('Cache Key Generation', () => {
    test('should generate consistent keys for same URL and params', async () => {
      const testData1 = { id: 1 }
      const testData2 = { id: 2 }
      
      await cache.set('/api/users', testData1, { params: { page: 1, limit: 10 } })
      await cache.set('/api/users', testData2, { params: { page: 2, limit: 10 } })
      
      const result1 = await cache.get('/api/users', { page: 1, limit: 10 })
      const result2 = await cache.get('/api/users', { page: 2, limit: 10 })
      
      expect(result1).toEqual(testData1)
      expect(result2).toEqual(testData2)
    })

    test('should handle parameter order independence', async () => {
      const testData = { id: 1 }
      
      await cache.set('/api/users', testData, { params: { limit: 10, page: 1 } })
      const result = await cache.get('/api/users', { page: 1, limit: 10 })
      
      expect(result).toEqual(testData)
    })
  })

  describe('Cache Invalidation', () => {
    test('should invalidate specific entries', async () => {
      await cache.set('/api/users/1', { id: 1 })
      await cache.set('/api/users/2', { id: 2 })
      
      cache.invalidate('/api/users/1')
      
      expect(await cache.get('/api/users/1')).toBeNull()
      expect(await cache.get('/api/users/2')).toEqual({ id: 2 })
    })

    test('should invalidate entries by pattern', async () => {
      await cache.set('/api/users/1', { id: 1 })
      await cache.set('/api/users/2', { id: 2 })
      await cache.set('/api/posts/1', { id: 1 })
      
      cache.invalidatePattern(/\/api\/users\//)
      
      expect(await cache.get('/api/users/1')).toBeNull()
      expect(await cache.get('/api/users/2')).toBeNull()
      expect(await cache.get('/api/posts/1')).toEqual({ id: 1 })
    })

    test('should clear all entries', async () => {
      await cache.set('/api/users/1', { id: 1 })
      await cache.set('/api/users/2', { id: 2 })
      
      cache.clear()
      
      expect(await cache.get('/api/users/1')).toBeNull()
      expect(await cache.get('/api/users/2')).toBeNull()
    })
  })

  describe('Cache Size Management', () => {
    test('should enforce max entries limit', async () => {
      // Fill cache to max capacity
      for (let i = 1; i <= 5; i++) {
        await cache.set(`/api/users/${i}`, { id: i })
      }
      
      // Add one more entry (should evict oldest)
      await cache.set('/api/users/6', { id: 6 })
      
      // First entry should be evicted
      expect(await cache.get('/api/users/1')).toBeNull()
      expect(await cache.get('/api/users/6')).toEqual({ id: 6 })
    })
  })

  describe('Cache Statistics', () => {
    test('should provide cache statistics', async () => {
      await cache.set('/api/users/1', { id: 1 })
      await cache.set('/api/users/2', { id: 2 })
      
      const stats = cache.getStats()
      
      expect(stats.size).toBe(2)
      expect(stats.maxEntries).toBe(5)
      expect(stats.entries).toHaveLength(2)
      expect(stats.entries[0]).toHaveProperty('key')
      expect(stats.entries[0]).toHaveProperty('age')
      expect(stats.entries[0]).toHaveProperty('ttl')
    })
  })
})

describe('cachedFetch', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    userCache.clear()
  })

  test('should fetch and cache successful responses', async () => {
    const mockData = { id: 1, name: 'Test User' }
    ;(global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: () => Promise.resolve(mockData),
      headers: new Map([['etag', 'test-etag']])
    })

    const result = await cachedFetch('/api/users/1')
    
    expect(result).toEqual(mockData)
    expect(global.fetch).toHaveBeenCalledTimes(1)
    
    // Second call should use cache
    const cachedResult = await cachedFetch('/api/users/1')
    expect(cachedResult).toEqual(mockData)
    expect(global.fetch).toHaveBeenCalledTimes(1) // Still only 1 call
  })

  test('should throw error for failed requests', async () => {
    ;(global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: false,
      status: 404,
      statusText: 'Not Found'
    })

    await expect(cachedFetch('/api/users/999')).rejects.toThrow('HTTP 404: Not Found')
  })

  test('should use custom cache and TTL', async () => {
    const mockData = { stats: { users: 100 } }
    ;(global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: () => Promise.resolve(mockData)
    })

    await cachedFetch('/api/dashboard/stats', {
      cache: dashboardCache,
      cacheTTL: 5000
    })

    // Verify it's cached in the dashboard cache
    const cached = await dashboardCache.get('/api/dashboard/stats')
    expect(cached).toEqual(mockData)
  })
})

describe('Cache Invalidation Helpers', () => {
  beforeEach(() => {
    userCache.clear()
    employeeCache.clear()
  })

  test('invalidateUserCache should clear user-related entries', async () => {
    await userCache.set('/api/auth/me', { id: 1 })
    await userCache.set('/api/users/1', { id: 1 })
    await userCache.set('/api/posts/1', { id: 1 })
    
    invalidateUserCache()
    
    expect(await userCache.get('/api/auth/me')).toBeNull()
    expect(await userCache.get('/api/users/1')).toBeNull()
    expect(await userCache.get('/api/posts/1')).toEqual({ id: 1 }) // Should remain
  })

  test('invalidateEmployeeCache should clear specific employee or all employees', async () => {
    await employeeCache.set('/api/employees/1', { id: 1 })
    await employeeCache.set('/api/employees/2', { id: 2 })
    
    // Clear specific employee
    invalidateEmployeeCache('1')
    
    expect(await employeeCache.get('/api/employees/1')).toBeNull()
    expect(await employeeCache.get('/api/employees/2')).toEqual({ id: 2 })
    
    // Clear all employees
    invalidateEmployeeCache()
    
    expect(await employeeCache.get('/api/employees/2')).toBeNull()
  })
})

describe('Persistent Cache', () => {
  beforeEach(() => {
    localStorage.clear()
  })

  test('should set and get persistent cache entries', () => {
    const testData = { id: 1, name: 'Test' }
    
    persistentCache.set('test-key', testData)
    const result = persistentCache.get('test-key')
    
    expect(result).toEqual(testData)
  })

  test('should handle cache expiration in persistent storage', () => {
    const testData = { id: 1, name: 'Test' }
    
    persistentCache.set('test-key', testData, 100) // 100ms TTL
    
    // Should exist immediately
    expect(persistentCache.get('test-key')).toEqual(testData)
    
    // Mock time passage
    jest.spyOn(Date, 'now').mockReturnValue(Date.now() + 200)
    
    // Should be expired
    expect(persistentCache.get('test-key')).toBeNull()
    
    jest.restoreAllMocks()
  })

  test('should handle localStorage errors gracefully', () => {
    // Mock localStorage to throw error
    const originalSetItem = localStorage.setItem
    localStorage.setItem = jest.fn(() => {
      throw new Error('Storage quota exceeded')
    })
    
    // Should not throw
    expect(() => persistentCache.set('test-key', { data: 'test' })).not.toThrow()
    
    localStorage.setItem = originalSetItem
  })

  test('should remove and clear entries', () => {
    persistentCache.set('key1', { id: 1 })
    persistentCache.set('key2', { id: 2 })
    
    persistentCache.remove('key1')
    expect(persistentCache.get('key1')).toBeNull()
    expect(persistentCache.get('key2')).toEqual({ id: 2 })
    
    persistentCache.clear()
    expect(persistentCache.get('key2')).toBeNull()
  })
})
