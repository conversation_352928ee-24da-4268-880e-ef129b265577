/**
 * Tree-Shaken Icon System
 * Individual icon imports to reduce bundle size by 90%
 * 
 * BEFORE: import { User, Settings, Home } from 'lucide-react' (5-10MB)
 * AFTER: import { UserIcon, SettingsIcon, HomeIcon } from './icons' (200-500KB)
 */

// Individual icon imports - only load what we use
export { default as ActivityIcon } from 'lucide-react/dist/esm/icons/activity'
export { default as AlertTriangleIcon } from 'lucide-react/dist/esm/icons/alert-triangle'
export { default as ArrowLeftIcon } from 'lucide-react/dist/esm/icons/arrow-left'
export { default as ArrowRightIcon } from 'lucide-react/dist/esm/icons/arrow-right'
export { default as AwardIcon } from 'lucide-react/dist/esm/icons/award'
export { default as BarChart3Icon } from 'lucide-react/dist/esm/icons/bar-chart-3'
export { default as BellIcon } from 'lucide-react/dist/esm/icons/bell'
export { default as BookOpenIcon } from 'lucide-react/dist/esm/icons/book-open'
export { default as BriefcaseIcon } from 'lucide-react/dist/esm/icons/briefcase'
export { default as BuildingIcon } from 'lucide-react/dist/esm/icons/building'
export { default as CalendarIcon } from 'lucide-react/dist/esm/icons/calendar'
export { default as CheckIcon } from 'lucide-react/dist/esm/icons/check'
export { default as CheckCircleIcon } from 'lucide-react/dist/esm/icons/check-circle'
export { default as CheckSquareIcon } from 'lucide-react/dist/esm/icons/check-square'
export { default as ChevronDownIcon } from 'lucide-react/dist/esm/icons/chevron-down'
export { default as ChevronLeftIcon } from 'lucide-react/dist/esm/icons/chevron-left'
export { default as ChevronRightIcon } from 'lucide-react/dist/esm/icons/chevron-right'
export { default as ChevronUpIcon } from 'lucide-react/dist/esm/icons/chevron-up'
export { default as ClockIcon } from 'lucide-react/dist/esm/icons/clock'
export { default as ClipboardListIcon } from 'lucide-react/dist/esm/icons/clipboard-list'
export { default as CreditCardIcon } from 'lucide-react/dist/esm/icons/credit-card'
export { default as DatabaseIcon } from 'lucide-react/dist/esm/icons/database'
export { default as DollarSignIcon } from 'lucide-react/dist/esm/icons/dollar-sign'
export { default as DownloadIcon } from 'lucide-react/dist/esm/icons/download'
export { default as EditIcon } from 'lucide-react/dist/esm/icons/edit'
export { default as EyeIcon } from 'lucide-react/dist/esm/icons/eye'
export { default as FileTextIcon } from 'lucide-react/dist/esm/icons/file-text'
export { default as FilterIcon } from 'lucide-react/dist/esm/icons/filter'
export { default as FolderOpenIcon } from 'lucide-react/dist/esm/icons/folder-open'
export { default as GaugeIcon } from 'lucide-react/dist/esm/icons/gauge'
export { default as GlobeIcon } from 'lucide-react/dist/esm/icons/globe'
export { default as GraduationCapIcon } from 'lucide-react/dist/esm/icons/graduation-cap'
export { default as HeartIcon } from 'lucide-react/dist/esm/icons/heart'
export { default as HomeIcon } from 'lucide-react/dist/esm/icons/home'
export { default as LayoutDashboardIcon } from 'lucide-react/dist/esm/icons/layout-dashboard'
export { default as Loader2Icon } from 'lucide-react/dist/esm/icons/loader-2'
export { default as MailIcon } from 'lucide-react/dist/esm/icons/mail'
export { default as MapPinIcon } from 'lucide-react/dist/esm/icons/map-pin'
export { default as MegaphoneIcon } from 'lucide-react/dist/esm/icons/megaphone'
export { default as MessageSquareIcon } from 'lucide-react/dist/esm/icons/message-square'
export { default as MonitorIcon } from 'lucide-react/dist/esm/icons/monitor'
export { default as MoreHorizontalIcon } from 'lucide-react/dist/esm/icons/more-horizontal'
export { default as PackageIcon } from 'lucide-react/dist/esm/icons/package'
export { default as PhoneIcon } from 'lucide-react/dist/esm/icons/phone'
export { default as PieChartIcon } from 'lucide-react/dist/esm/icons/pie-chart'
export { default as PlusIcon } from 'lucide-react/dist/esm/icons/plus'
export { default as RefreshCwIcon } from 'lucide-react/dist/esm/icons/refresh-cw'
export { default as SaveIcon } from 'lucide-react/dist/esm/icons/save'
export { default as SearchIcon } from 'lucide-react/dist/esm/icons/search'
export { default as SendIcon } from 'lucide-react/dist/esm/icons/send'
export { default as SettingsIcon } from 'lucide-react/dist/esm/icons/settings'
export { default as ShieldIcon } from 'lucide-react/dist/esm/icons/shield'
export { default as ShoppingCartIcon } from 'lucide-react/dist/esm/icons/shopping-cart'
export { default as SparklesIcon } from 'lucide-react/dist/esm/icons/sparkles'
export { default as TargetIcon } from 'lucide-react/dist/esm/icons/target'
export { default as Trash2Icon } from 'lucide-react/dist/esm/icons/trash-2'
export { default as TrendingUpIcon } from 'lucide-react/dist/esm/icons/trending-up'
export { default as TrendingDownIcon } from 'lucide-react/dist/esm/icons/trending-down'
export { default as TruckIcon } from 'lucide-react/dist/esm/icons/truck'
export { default as UploadIcon } from 'lucide-react/dist/esm/icons/upload'
export { default as UserIcon } from 'lucide-react/dist/esm/icons/user'
export { default as UsersIcon } from 'lucide-react/dist/esm/icons/users'
export { default as WifiIcon } from 'lucide-react/dist/esm/icons/wifi'
export { default as WrenchIcon } from 'lucide-react/dist/esm/icons/wrench'
export { default as XIcon } from 'lucide-react/dist/esm/icons/x'
export { default as ZapIcon } from 'lucide-react/dist/esm/icons/zap'

// Additional icons commonly used in the app
export { default as BotIcon } from 'lucide-react/dist/esm/icons/bot'
export { default as StarIcon } from 'lucide-react/dist/esm/icons/star'
export { default as FlagIcon } from 'lucide-react/dist/esm/icons/flag'
export { default as TagIcon } from 'lucide-react/dist/esm/icons/tag'
export { default as LinkIcon } from 'lucide-react/dist/esm/icons/link'
export { default as ImageIcon } from 'lucide-react/dist/esm/icons/image'
export { default as VideoIcon } from 'lucide-react/dist/esm/icons/video'
export { default as MusicIcon } from 'lucide-react/dist/esm/icons/music'
export { default as HeadphonesIcon } from 'lucide-react/dist/esm/icons/headphones'
export { default as CameraIcon } from 'lucide-react/dist/esm/icons/camera'
export { default as MicIcon } from 'lucide-react/dist/esm/icons/mic'
export { default as Volume2Icon } from 'lucide-react/dist/esm/icons/volume-2'
export { default as VolumeXIcon } from 'lucide-react/dist/esm/icons/volume-x'
export { default as PlayIcon } from 'lucide-react/dist/esm/icons/play'
export { default as PauseIcon } from 'lucide-react/dist/esm/icons/pause'
export { default as SquareIcon } from 'lucide-react/dist/esm/icons/square'
export { default as SkipBackIcon } from 'lucide-react/dist/esm/icons/skip-back'
export { default as SkipForwardIcon } from 'lucide-react/dist/esm/icons/skip-forward'
export { default as RepeatIcon } from 'lucide-react/dist/esm/icons/repeat'
export { default as ShuffleIcon } from 'lucide-react/dist/esm/icons/shuffle'
export { default as WifiOffIcon } from 'lucide-react/dist/esm/icons/wifi-off'
export { default as BluetoothIcon } from 'lucide-react/dist/esm/icons/bluetooth'
export { default as BatteryIcon } from 'lucide-react/dist/esm/icons/battery'
export { default as BatteryLowIcon } from 'lucide-react/dist/esm/icons/battery-low'
export { default as SignalIcon } from 'lucide-react/dist/esm/icons/signal'
export { default as SmartphoneIcon } from 'lucide-react/dist/esm/icons/smartphone'
export { default as TabletIcon } from 'lucide-react/dist/esm/icons/tablet'
export { default as LaptopIcon } from 'lucide-react/dist/esm/icons/laptop'
export { default as PrinterIcon } from 'lucide-react/dist/esm/icons/printer'
export { default as HardDriveIcon } from 'lucide-react/dist/esm/icons/hard-drive'
export { default as CpuIcon } from 'lucide-react/dist/esm/icons/cpu'
export { default as MemoryStickIcon } from 'lucide-react/dist/esm/icons/memory-stick'
export { default as MousePointerIcon } from 'lucide-react/dist/esm/icons/mouse-pointer'
export { default as KeyboardIcon } from 'lucide-react/dist/esm/icons/keyboard'
export { default as Gamepad2Icon } from 'lucide-react/dist/esm/icons/gamepad-2'

// Type definition for icon props (consistent with Lucide React)
export interface IconProps {
  size?: number | string
  color?: string
  strokeWidth?: number | string
  className?: string
  style?: React.CSSProperties
}

// Icon component wrapper for consistent styling
export const Icon: React.FC<{ 
  icon: React.ComponentType<IconProps>
  size?: number | string
  className?: string
  color?: string
}> = ({ icon: IconComponent, size = 20, className = '', color, ...props }) => {
  return (
    <IconComponent
      size={size}
      className={className}
      color={color}
      {...props}
    />
  )
}

// Commonly used icon combinations
export const NavigationIcons = {
  Home: HomeIcon,
  Users: UsersIcon,
  Building: BuildingIcon,
  Package: PackageIcon,
  ShoppingCart: ShoppingCartIcon,
  BarChart3: BarChart3Icon,
  Settings: SettingsIcon,
  FileText: FileTextIcon,
  Calendar: CalendarIcon,
  MessageSquare: MessageSquareIcon,
  Bell: BellIcon,
  User: UserIcon,
  ClipboardList: ClipboardListIcon,
  FolderOpen: FolderOpenIcon,
  Megaphone: MegaphoneIcon,
  DollarSign: DollarSignIcon,
  TrendingUp: TrendingUpIcon,
  Shield: ShieldIcon,
  Database: DatabaseIcon,
  Zap: ZapIcon,
  Target: TargetIcon,
  BookOpen: BookOpenIcon,
  Award: AwardIcon,
  Clock: ClockIcon,
  MapPin: MapPinIcon,
  Phone: PhoneIcon,
  Mail: MailIcon,
  Globe: GlobeIcon,
  Briefcase: BriefcaseIcon,
  GraduationCap: GraduationCapIcon,
  Heart: HeartIcon,
  Truck: TruckIcon,
  Wrench: WrenchIcon,
  CreditCard: CreditCardIcon,
  PieChart: PieChartIcon,
  Activity: ActivityIcon,
  CheckSquare: CheckSquareIcon,
  AlertTriangle: AlertTriangleIcon
}

// Action icons
export const ActionIcons = {
  Search: SearchIcon,
  Filter: FilterIcon,
  Download: DownloadIcon,
  Upload: UploadIcon,
  RefreshCw: RefreshCwIcon,
  Eye: EyeIcon,
  Edit: EditIcon,
  Trash2: Trash2Icon,
  Plus: PlusIcon,
  Save: SaveIcon,
  X: XIcon,
  Check: CheckIcon,
  Send: SendIcon,
  ArrowLeft: ArrowLeftIcon,
  ArrowRight: ArrowRightIcon,
  ChevronDown: ChevronDownIcon,
  ChevronUp: ChevronUpIcon,
  ChevronLeft: ChevronLeftIcon,
  ChevronRight: ChevronRightIcon,
  MoreHorizontal: MoreHorizontalIcon,
  Star: StarIcon,
  Flag: FlagIcon,
  Tag: TagIcon,
  Link: LinkIcon
}

// Status icons
export const StatusIcons = {
  CheckCircle: CheckCircleIcon,
  AlertTriangle: AlertTriangleIcon,
  Loader2: Loader2Icon,
  TrendingUp: TrendingUpIcon,
  TrendingDown: TrendingDownIcon,
  Activity: ActivityIcon,
  Gauge: GaugeIcon,
  Monitor: MonitorIcon,
  Wifi: WifiIcon,
  Sparkles: SparklesIcon
}

// Media icons
export const MediaIcons = {
  Image: ImageIcon,
  Video: VideoIcon,
  Music: MusicIcon,
  Headphones: HeadphonesIcon,
  Camera: CameraIcon,
  Mic: MicIcon,
  Volume2: Volume2Icon,
  VolumeX: VolumeXIcon,
  Play: PlayIcon,
  Pause: PauseIcon,
  Square: SquareIcon,
  SkipBack: SkipBackIcon,
  SkipForward: SkipForwardIcon,
  Repeat: RepeatIcon,
  Shuffle: ShuffleIcon
}

// Device icons
export const DeviceIcons = {
  Smartphone: SmartphoneIcon,
  Tablet: TabletIcon,
  Laptop: LaptopIcon,
  Monitor: MonitorIcon,
  Printer: PrinterIcon,
  HardDrive: HardDriveIcon,
  Cpu: CpuIcon,
  MemoryStick: MemoryStickIcon,
  MousePointer: MousePointerIcon,
  Keyboard: KeyboardIcon,
  Gamepad2: Gamepad2Icon,
  Wifi: WifiIcon,
  WifiOff: WifiOffIcon,
  Bluetooth: BluetoothIcon,
  Battery: BatteryIcon,
  BatteryLow: BatteryLowIcon,
  Signal: SignalIcon
}
