/**
 * Optimized Image Component
 * High-performance image component with lazy loading, modern formats, and responsive sizing
 */

import React, { useState, useRef, useEffect } from 'react'
import { 
  useImageOptimization, 
  generateResponsiveImageSources,
  createLazyLoadObserver,
  trackAssetPerformance,
  IMAGE_OPTIMIZATION 
} from '../../utils/assetOptimization'

interface OptimizedImageProps {
  src: string
  alt: string
  className?: string
  width?: number
  height?: number
  sizes?: string
  priority?: boolean
  quality?: keyof typeof IMAGE_OPTIMIZATION.QUALITY
  placeholder?: 'blur' | 'empty' | string
  onLoad?: () => void
  onError?: () => void
  lazy?: boolean
  responsive?: boolean
}

export const OptimizedImage: React.FC<OptimizedImageProps> = ({
  src,
  alt,
  className = '',
  width,
  height,
  sizes,
  priority = false,
  quality = 'preview',
  placeholder = 'empty',
  onLoad,
  onError,
  lazy = true,
  responsive = true
}) => {
  const [isLoaded, setIsLoaded] = useState(false)
  const [isInView, setIsInView] = useState(!lazy || priority)
  const [hasError, setHasError] = useState(false)
  const imgRef = useRef<HTMLImageElement>(null)
  const { getOptimalImageSrc } = useImageOptimization()
  
  // Lazy loading setup
  useEffect(() => {
    if (!lazy || priority || isInView) return
    
    const observer = createLazyLoadObserver((entry) => {
      if (entry.isIntersecting) {
        setIsInView(true)
        observer.disconnect()
      }
    })
    
    if (imgRef.current) {
      observer.observe(imgRef.current)
    }
    
    return () => observer.disconnect()
  }, [lazy, priority, isInView])
  
  // Performance tracking
  const trackLoadTime = (startTime: number) => {
    trackAssetPerformance(src, startTime)
  }
  
  // Handle image load
  const handleLoad = () => {
    setIsLoaded(true)
    onLoad?.()
  }
  
  // Handle image error
  const handleError = () => {
    setHasError(true)
    onError?.()
  }
  
  // Generate optimized sources
  const getImageSources = () => {
    if (!responsive) {
      return {
        src: getOptimalImageSrc(src.replace(/\.[^/.]+$/, ''), src),
        srcSet: undefined
      }
    }
    
    const basePath = src.replace(/\.[^/.]+$/, '')
    const sources = generateResponsiveImageSources(basePath, alt, sizes)
    
    return {
      src: getOptimalImageSrc(basePath, src),
      srcSet: sources.webp.join(', '),
      sizes: sources.sizes
    }
  }
  
  // Placeholder component
  const PlaceholderComponent = () => {
    if (placeholder === 'empty') {
      return (
        <div 
          className={`bg-gray-200 animate-pulse ${className}`}
          style={{ width, height }}
        />
      )
    }
    
    if (placeholder === 'blur') {
      return (
        <div 
          className={`bg-gradient-to-r from-gray-200 to-gray-300 animate-pulse ${className}`}
          style={{ width, height }}
        />
      )
    }
    
    // Custom placeholder image
    return (
      <img
        src={placeholder}
        alt={`${alt} placeholder`}
        className={`filter blur-sm ${className}`}
        style={{ width, height }}
      />
    )
  }
  
  // Error fallback
  const ErrorFallback = () => (
    <div 
      className={`bg-gray-100 border-2 border-dashed border-gray-300 flex items-center justify-center ${className}`}
      style={{ width, height }}
    >
      <div className="text-center text-gray-500">
        <svg 
          className="mx-auto h-8 w-8 mb-2" 
          fill="none" 
          stroke="currentColor" 
          viewBox="0 0 24 24"
        >
          <path 
            strokeLinecap="round" 
            strokeLinejoin="round" 
            strokeWidth={2} 
            d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" 
          />
        </svg>
        <p className="text-xs">Image failed to load</p>
      </div>
    </div>
  )
  
  // Don't render anything if not in view and lazy loading
  if (lazy && !priority && !isInView) {
    return (
      <div 
        ref={imgRef}
        className={className}
        style={{ width, height }}
      >
        <PlaceholderComponent />
      </div>
    )
  }
  
  // Show error fallback
  if (hasError) {
    return <ErrorFallback />
  }
  
  const { src: optimizedSrc, srcSet, sizes: responsiveSizes } = getImageSources()
  const startTime = performance.now()
  
  return (
    <div className="relative">
      {/* Show placeholder while loading */}
      {!isLoaded && <PlaceholderComponent />}
      
      {/* Optimized image */}
      <img
        ref={imgRef}
        src={optimizedSrc}
        srcSet={responsive ? srcSet : undefined}
        sizes={responsive ? responsiveSizes : undefined}
        alt={alt}
        width={width}
        height={height}
        className={`${className} ${isLoaded ? 'opacity-100' : 'opacity-0'} transition-opacity duration-300`}
        loading={priority ? 'eager' : 'lazy'}
        decoding="async"
        onLoad={() => {
          handleLoad()
          trackLoadTime(startTime)
        }}
        onError={handleError}
        style={{
          width,
          height,
          objectFit: 'cover',
          ...(isLoaded ? {} : { position: 'absolute', top: 0, left: 0 })
        }}
      />
    </div>
  )
}

// Avatar component with optimized loading
export const OptimizedAvatar: React.FC<{
  src?: string
  alt: string
  size?: 'sm' | 'md' | 'lg' | 'xl'
  className?: string
  fallback?: string
}> = ({ 
  src, 
  alt, 
  size = 'md', 
  className = '', 
  fallback 
}) => {
  const sizeClasses = {
    sm: 'w-8 h-8',
    md: 'w-12 h-12',
    lg: 'w-16 h-16',
    xl: 'w-24 h-24'
  }
  
  const sizePixels = {
    sm: 32,
    md: 48,
    lg: 64,
    xl: 96
  }
  
  if (!src) {
    return (
      <div className={`${sizeClasses[size]} bg-gray-300 rounded-full flex items-center justify-center ${className}`}>
        <span className="text-gray-600 font-medium">
          {alt.charAt(0).toUpperCase()}
        </span>
      </div>
    )
  }
  
  return (
    <OptimizedImage
      src={src}
      alt={alt}
      width={sizePixels[size]}
      height={sizePixels[size]}
      className={`${sizeClasses[size]} rounded-full object-cover ${className}`}
      quality="thumbnail"
      priority={size === 'sm'} // Small avatars are often above fold
      placeholder={fallback || 'empty'}
      responsive={false} // Avatars don't need responsive sizing
    />
  )
}

// Logo component with optimized loading
export const OptimizedLogo: React.FC<{
  src: string
  alt: string
  width?: number
  height?: number
  className?: string
  priority?: boolean
}> = ({ 
  src, 
  alt, 
  width = 120, 
  height = 40, 
  className = '', 
  priority = true 
}) => {
  return (
    <OptimizedImage
      src={src}
      alt={alt}
      width={width}
      height={height}
      className={className}
      priority={priority}
      quality="full"
      responsive={false}
      lazy={false}
    />
  )
}

// Icon component with optimized loading
export const OptimizedIcon: React.FC<{
  src: string
  alt: string
  size?: number
  className?: string
}> = ({ 
  src, 
  alt, 
  size = 24, 
  className = '' 
}) => {
  return (
    <OptimizedImage
      src={src}
      alt={alt}
      width={size}
      height={size}
      className={className}
      quality="thumbnail"
      priority={false}
      responsive={false}
      lazy={true}
    />
  )
}

export default OptimizedImage
