/**
 * Redux Store Integration Tests
 * Comprehensive testing for Redux store, actions, and reducers
 */

import { configureStore } from '@reduxjs/toolkit'
import { Provider } from 'react-redux'
import { renderHook } from '@testing-library/react'
import React from 'react'

import { store, RootState } from '../store'
import authReducer, { 
  loginStart, 
  loginSuccess, 
  loginFailure, 
  logout,
  clearError 
} from '../slices/authSlice'
import employeeReducer, {
  fetchEmployeesStart,
  fetchEmployeesSuccess,
  fetchEmployeesFailure,
  addEmployee,
  updateEmployee,
  deleteEmployee
} from '../slices/employeeSlice'
import { useAppDispatch, useAppSelector } from '../hooks'

// Mock API responses
const mockUser = {
  id: '1',
  name: '<PERSON>',
  email: '<EMAIL>',
  role: {
    id: 'admin',
    name: 'Administrator',
    permissions: ['read', 'write', 'delete']
  }
}

const mockEmployees = [
  {
    id: '1',
    name: '<PERSON>',
    email: '<EMAIL>',
    department: 'Engineering',
    position: 'Senior Developer',
    status: 'active' as const
  },
  {
    id: '2',
    name: '<PERSON>',
    email: '<EMAIL>',
    department: 'Marketing',
    position: 'Marketing Manager',
    status: 'active' as const
  }
]

// Test store factory
const createTestStore = () => {
  return configureStore({
    reducer: {
      auth: authReducer,
      employees: employeeReducer
    },
    middleware: (getDefaultMiddleware) =>
      getDefaultMiddleware({
        serializableCheck: {
          ignoredActions: ['persist/PERSIST']
        }
      })
  })
}

// Test wrapper component
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const testStore = createTestStore()
  return <Provider store={testStore}>{children}</Provider>
}

describe('Redux Store Integration', () => {
  describe('Store Configuration', () => {
    test('should have correct initial state', () => {
      const state = store.getState()
      
      expect(state.auth.isAuthenticated).toBe(false)
      expect(state.auth.user).toBeNull()
      expect(state.auth.loading).toBe(false)
      expect(state.auth.error).toBeNull()
      
      expect(state.employees.employees).toEqual([])
      expect(state.employees.loading).toBe(false)
      expect(state.employees.error).toBeNull()
    })

    test('should handle multiple reducers', () => {
      const testStore = createTestStore()
      const state = testStore.getState()
      
      expect(state).toHaveProperty('auth')
      expect(state).toHaveProperty('employees')
    })
  })

  describe('Auth Slice Integration', () => {
    test('should handle complete login flow', () => {
      const testStore = createTestStore()
      
      // Start login
      testStore.dispatch(loginStart())
      expect(testStore.getState().auth.loading).toBe(true)
      expect(testStore.getState().auth.error).toBeNull()
      
      // Login success
      testStore.dispatch(loginSuccess(mockUser))
      expect(testStore.getState().auth.loading).toBe(false)
      expect(testStore.getState().auth.isAuthenticated).toBe(true)
      expect(testStore.getState().auth.user).toEqual(mockUser)
      
      // Logout
      testStore.dispatch(logout())
      expect(testStore.getState().auth.isAuthenticated).toBe(false)
      expect(testStore.getState().auth.user).toBeNull()
    })

    test('should handle login failure', () => {
      const testStore = createTestStore()
      const errorMessage = 'Invalid credentials'
      
      testStore.dispatch(loginStart())
      testStore.dispatch(loginFailure(errorMessage))
      
      expect(testStore.getState().auth.loading).toBe(false)
      expect(testStore.getState().auth.isAuthenticated).toBe(false)
      expect(testStore.getState().auth.error).toBe(errorMessage)
    })

    test('should clear errors', () => {
      const testStore = createTestStore()
      
      testStore.dispatch(loginFailure('Test error'))
      expect(testStore.getState().auth.error).toBe('Test error')
      
      testStore.dispatch(clearError())
      expect(testStore.getState().auth.error).toBeNull()
    })
  })

  describe('Employee Slice Integration', () => {
    test('should handle employee fetch flow', () => {
      const testStore = createTestStore()
      
      // Start fetch
      testStore.dispatch(fetchEmployeesStart())
      expect(testStore.getState().employees.loading).toBe(true)
      
      // Fetch success
      testStore.dispatch(fetchEmployeesSuccess(mockEmployees))
      expect(testStore.getState().employees.loading).toBe(false)
      expect(testStore.getState().employees.employees).toEqual(mockEmployees)
    })

    test('should handle employee fetch failure', () => {
      const testStore = createTestStore()
      const errorMessage = 'Failed to fetch employees'
      
      testStore.dispatch(fetchEmployeesStart())
      testStore.dispatch(fetchEmployeesFailure(errorMessage))
      
      expect(testStore.getState().employees.loading).toBe(false)
      expect(testStore.getState().employees.error).toBe(errorMessage)
    })

    test('should add new employee', () => {
      const testStore = createTestStore()
      const newEmployee = {
        id: '3',
        name: 'Charlie Brown',
        email: '<EMAIL>',
        department: 'Sales',
        position: 'Sales Representative',
        status: 'active' as const
      }
      
      // Set initial employees
      testStore.dispatch(fetchEmployeesSuccess(mockEmployees))
      
      // Add new employee
      testStore.dispatch(addEmployee(newEmployee))
      
      const employees = testStore.getState().employees.employees
      expect(employees).toHaveLength(3)
      expect(employees).toContainEqual(newEmployee)
    })

    test('should update existing employee', () => {
      const testStore = createTestStore()
      const updatedEmployee = {
        ...mockEmployees[0],
        name: 'Alice Johnson',
        position: 'Lead Developer'
      }
      
      // Set initial employees
      testStore.dispatch(fetchEmployeesSuccess(mockEmployees))
      
      // Update employee
      testStore.dispatch(updateEmployee(updatedEmployee))
      
      const employees = testStore.getState().employees.employees
      const updated = employees.find(emp => emp.id === '1')
      expect(updated?.name).toBe('Alice Johnson')
      expect(updated?.position).toBe('Lead Developer')
    })

    test('should delete employee', () => {
      const testStore = createTestStore()
      
      // Set initial employees
      testStore.dispatch(fetchEmployeesSuccess(mockEmployees))
      
      // Delete employee
      testStore.dispatch(deleteEmployee('1'))
      
      const employees = testStore.getState().employees.employees
      expect(employees).toHaveLength(1)
      expect(employees.find(emp => emp.id === '1')).toBeUndefined()
    })
  })

  describe('Redux Hooks Integration', () => {
    test('useAppSelector should work correctly', () => {
      const { result } = renderHook(
        () => useAppSelector(state => state.auth.isAuthenticated),
        { wrapper: TestWrapper }
      )
      
      expect(result.current).toBe(false)
    })

    test('useAppDispatch should work correctly', () => {
      const { result } = renderHook(
        () => {
          const dispatch = useAppDispatch()
          const isAuthenticated = useAppSelector(state => state.auth.isAuthenticated)
          return { dispatch, isAuthenticated }
        },
        { wrapper: TestWrapper }
      )
      
      expect(result.current.isAuthenticated).toBe(false)
      
      // Dispatch login success
      result.current.dispatch(loginSuccess(mockUser))
      
      expect(result.current.isAuthenticated).toBe(true)
    })
  })

  describe('State Persistence', () => {
    test('should handle state rehydration', () => {
      // Mock persisted state
      const persistedState = {
        auth: {
          isAuthenticated: true,
          user: mockUser,
          loading: false,
          error: null
        }
      }
      
      const testStore = configureStore({
        reducer: {
          auth: authReducer,
          employees: employeeReducer
        },
        preloadedState: persistedState
      })
      
      expect(testStore.getState().auth.isAuthenticated).toBe(true)
      expect(testStore.getState().auth.user).toEqual(mockUser)
    })
  })

  describe('Cross-Slice Interactions', () => {
    test('should handle logout clearing all user data', () => {
      const testStore = createTestStore()
      
      // Set up authenticated state with employee data
      testStore.dispatch(loginSuccess(mockUser))
      testStore.dispatch(fetchEmployeesSuccess(mockEmployees))
      
      expect(testStore.getState().auth.isAuthenticated).toBe(true)
      expect(testStore.getState().employees.employees).toHaveLength(2)
      
      // Logout should clear auth but not necessarily employee data
      testStore.dispatch(logout())
      
      expect(testStore.getState().auth.isAuthenticated).toBe(false)
      expect(testStore.getState().auth.user).toBeNull()
      // Employee data might persist for caching purposes
    })
  })

  describe('Error Handling', () => {
    test('should handle concurrent errors in different slices', () => {
      const testStore = createTestStore()
      
      // Trigger errors in both slices
      testStore.dispatch(loginFailure('Auth error'))
      testStore.dispatch(fetchEmployeesFailure('Employee fetch error'))
      
      const state = testStore.getState()
      expect(state.auth.error).toBe('Auth error')
      expect(state.employees.error).toBe('Employee fetch error')
    })

    test('should handle error recovery', () => {
      const testStore = createTestStore()
      
      // Set error state
      testStore.dispatch(loginFailure('Network error'))
      expect(testStore.getState().auth.error).toBe('Network error')
      
      // Successful action should clear error
      testStore.dispatch(loginSuccess(mockUser))
      expect(testStore.getState().auth.error).toBeNull()
    })
  })

  describe('Performance Considerations', () => {
    test('should handle large datasets efficiently', () => {
      const testStore = createTestStore()
      
      // Generate large employee dataset
      const largeEmployeeList = Array.from({ length: 1000 }, (_, index) => ({
        id: `emp-${index}`,
        name: `Employee ${index}`,
        email: `employee${index}@example.com`,
        department: `Department ${index % 10}`,
        position: `Position ${index % 5}`,
        status: 'active' as const
      }))
      
      const startTime = performance.now()
      testStore.dispatch(fetchEmployeesSuccess(largeEmployeeList))
      const endTime = performance.now()
      
      expect(testStore.getState().employees.employees).toHaveLength(1000)
      expect(endTime - startTime).toBeLessThan(100) // Should complete within 100ms
    })

    test('should handle rapid state updates', () => {
      const testStore = createTestStore()
      
      // Rapid updates
      for (let i = 0; i < 100; i++) {
        testStore.dispatch(addEmployee({
          id: `rapid-${i}`,
          name: `Rapid Employee ${i}`,
          email: `rapid${i}@example.com`,
          department: 'Test',
          position: 'Tester',
          status: 'active'
        }))
      }
      
      expect(testStore.getState().employees.employees).toHaveLength(100)
    })
  })

  describe('Type Safety', () => {
    test('should maintain type safety with TypeScript', () => {
      const testStore = createTestStore()
      const state: RootState = testStore.getState()
      
      // These should compile without errors
      expect(typeof state.auth.isAuthenticated).toBe('boolean')
      expect(Array.isArray(state.employees.employees)).toBe(true)
      
      // Type checking for actions
      testStore.dispatch(loginSuccess(mockUser))
      testStore.dispatch(fetchEmployeesSuccess(mockEmployees))
    })
  })
})
