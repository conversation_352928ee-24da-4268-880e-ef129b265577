import { useState, useEffect } from 'react'
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import { useDispatch, useSelector } from 'react-redux'
import { Toaster } from 'react-hot-toast'
import type { AppDispatch, RootState } from './store'
import { verifyToken } from './store/slices/authSlice'
import { fetchNotifications } from './store/slices/notificationSlice'
import { fetchDashboardLayout } from './store/slices/dashboardSlice'
import Layout from './components/Layout'
import ErrorBoundary from './components/ErrorBoundary'
import Login from './pages/Login'
import Home from './pages/Home'
import HowItWorks from './pages/HowItWorks'
import RoleBasedRouter from './routes/RoleBasedRouter'
import { ModalProvider } from './contexts/ModalContext'
import ModalManager from './components/modals/ModalManager'
import { getPerformanceMonitor } from './utils/performanceMonitor'
import { testLodashImports } from './utils/testLodash'
// Removed unused imports - components and test utilities cleaned up





function App() {
  const dispatch = useDispatch<AppDispatch>()
  const { isAuthenticated, user, isLoading, token } = useSelector((state: RootState) => state.auth)
  const [language, setLanguage] = useState<'ar' | 'en'>('ar')
  const [hasTriedTokenVerification, setHasTriedTokenVerification] = useState(false)

  // Set document direction based on language and force CSS reload
  useEffect(() => {
    document.documentElement.dir = language === 'ar' ? 'rtl' : 'ltr'
    document.documentElement.lang = language

    // Force CSS cache busting
    const timestamp = Date.now()
    document.documentElement.setAttribute('data-css-version', timestamp.toString())

    // Add meta tag to prevent caching
    const metaTag = document.createElement('meta')
    metaTag.httpEquiv = 'Cache-Control'
    metaTag.content = 'no-cache, no-store, must-revalidate'
    document.head.appendChild(metaTag)

    // Force style recalculation
    document.body.style.display = 'none'
    void document.body.offsetHeight // Trigger reflow
    document.body.style.display = ''
  }, [language])

  // Verify token on app load - SIMPLIFIED AND ROBUST
  useEffect(() => {
    // Prevent multiple verification attempts
    if (hasTriedTokenVerification) return

    const storedToken = localStorage.getItem('access_token') || localStorage.getItem('token')

    if (storedToken) {
      console.log('🔍 Found stored token, attempting verification...')
      setHasTriedTokenVerification(true)

      // Only verify if we don't already have user data
      if (!user) {
        dispatch(verifyToken()).catch((error) => {
          console.error('🔍 Token verification failed:', error)
          // Clear invalid token
          localStorage.removeItem('token')
          localStorage.removeItem('access_token')
          localStorage.removeItem('refresh_token')
        })
      } else {
        console.log('🔍 Already have user data, skipping verification')
      }
    } else {
      console.log('🔍 No stored token found')
      setHasTriedTokenVerification(true)
    }
  }, [dispatch, hasTriedTokenVerification, user])

  // REMOVED: All timeout and reload logic that was causing issues
  // The app should handle authentication states naturally without forced reloads

  // Load user-specific data when authenticated
  useEffect(() => {
    if (isAuthenticated && user) {
      dispatch(fetchNotifications(user.role.id))
      dispatch(fetchDashboardLayout(user.role.id))
    }
  }, [dispatch, isAuthenticated, user])

  // Initialize performance monitoring
  useEffect(() => {
    const monitor = getPerformanceMonitor()

    // Send metrics to analytics after page load
    const timer = setTimeout(() => {
      monitor.sendMetricsToAnalytics()
    }, 3000)

    // Test lodash imports to verify module resolution
    if (process.env.NODE_ENV === 'development') {
      testLodashImports()
    }

    return () => clearTimeout(timer)
  }, [])

  // Removed export test functions - cleaned up debugging code

  // Debug logging
  console.log('App State:', {
    isLoading,
    isAuthenticated,
    hasUser: !!user,
    hasToken: !!token,
    hasTriedTokenVerification
  })

  // REMOVED: Inconsistent state logic that was clearing valid tokens
  // Let the natural auth flow handle state consistency

  // Show loading screen only when actively verifying token
  if (isLoading && !hasTriedTokenVerification && (token || localStorage.getItem('access_token'))) {
    console.log('App: Showing loading screen for token verification')
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
        <div className="text-center glass-card p-8 rounded-xl shadow-xl">
          <div className="animate-spin rounded-full h-24 w-24 border-b-2 border-white mx-auto mb-6"></div>
          <p className="text-white text-xl mb-4 font-medium">
            {language === 'ar' ? 'جاري تحميل بيانات المستخدم...' : 'Loading user data...'}
          </p>
          <p className="text-white/70 text-sm">
            {language === 'ar' ? 'التحقق من المصادقة...' : 'Verifying authentication...'}
          </p>
          {/* Loading progress bar */}
          <div className="w-full h-1 bg-white/10 rounded-full mt-6 overflow-hidden">
            <div className="h-full bg-gradient-to-r from-blue-500 to-purple-500 animate-pulse"
                 style={{width: '60%'}}></div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <ErrorBoundary>
      <ModalProvider>
        <Router>
          {!isAuthenticated ? (
            <Routes>
              <Route path="/login" element={<Login language={language} />} />
              <Route path="/how-it-works" element={<HowItWorks language={language} setLanguage={setLanguage} />} />
              <Route path="/home" element={<Home language={language} setLanguage={setLanguage} />} />
              {/* Redirect all other routes to login for unauthenticated users */}
              <Route path="/*" element={<Login language={language} />} />
            </Routes>
          ) : (
            <Layout language={language} setLanguage={setLanguage}>
              <RoleBasedRouter language={language} />
              {/* Modal Manager for centralized modal handling */}
              <ModalManager language={language} />
            </Layout>
          )}

          {/* Chat widgets removed - cleaned up unused components */}
        </Router>

        {/* Toast Notifications */}
        <Toaster
          position="top-right"
          toastOptions={{
            duration: 4000,
          style: {
            background: 'rgba(255, 255, 255, 0.1)',
            backdropFilter: 'blur(10px)',
            border: '1px solid rgba(255, 255, 255, 0.2)',
            color: '#fff',
            borderRadius: '12px',
            fontSize: '14px',
            fontWeight: '500'
          },
          success: {
            iconTheme: {
              primary: '#10b981',
              secondary: '#fff',
            },
          },
          error: {
            iconTheme: {
              primary: '#ef4444',
              secondary: '#fff',
            },
          },
        }}
      />
      </ModalProvider>
    </ErrorBoundary>
  )
}

export default App
