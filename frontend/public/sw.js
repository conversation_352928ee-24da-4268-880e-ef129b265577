/**
 * Service Worker for نمو EMS
 * Provides offline functionality and caching
 */

const CACHE_NAME = 'numu-ems-v2.0.0'
const STATIC_CACHE = 'numu-static-v2.0.0'
const DYNAMIC_CACHE = 'numu-dynamic-v2.0.0'
const API_CACHE = 'numu-api-v2.0.0'
const IMAGE_CACHE = 'numu-images-v2.0.0'
const FONT_CACHE = 'numu-fonts-v2.0.0'

// Cache configuration
const CACHE_CONFIG = {
  // Static assets - cache for 1 year
  STATIC_MAX_AGE: 365 * 24 * 60 * 60 * 1000, // 1 year

  // API responses - cache for 5 minutes
  API_MAX_AGE: 5 * 60 * 1000, // 5 minutes

  // Images - cache for 1 month
  IMAGE_MAX_AGE: 30 * 24 * 60 * 60 * 1000, // 1 month

  // Fonts - cache for 1 year
  FONT_MAX_AGE: 365 * 24 * 60 * 60 * 1000, // 1 year

  // Maximum cache sizes
  MAX_STATIC_ENTRIES: 100,
  MAX_API_ENTRIES: 50,
  MAX_IMAGE_ENTRIES: 200,
  MAX_FONT_ENTRIES: 20
}

// Files to cache immediately
const STATIC_FILES = [
  '/',
  '/index.html',
  '/manifest.json',
  '/offline.html',
  // Add critical CSS and JS files here
]

// API endpoints to cache
const API_CACHE_PATTERNS = [
  /\/api\/auth\/me/,
  /\/api\/dashboard\/stats/,
  /\/api\/employees\/\d+/,
  /\/api\/projects\/\d+/,
]

// Files that should always be fetched from network
const NETWORK_FIRST_PATTERNS = [
  /\/api\/auth\//,
  /\/api\/notifications\//,
  /\/api\/messages\//,
]

// Install event - cache static files
self.addEventListener('install', (event) => {
  console.log('[SW] Installing service worker...')
  
  event.waitUntil(
    caches.open(STATIC_CACHE)
      .then((cache) => {
        console.log('[SW] Caching static files')
        return cache.addAll(STATIC_FILES)
      })
      .then(() => {
        console.log('[SW] Static files cached successfully')
        return self.skipWaiting()
      })
      .catch((error) => {
        console.error('[SW] Failed to cache static files:', error)
      })
  )
})

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  console.log('[SW] Activating service worker...')
  
  event.waitUntil(
    caches.keys()
      .then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (cacheName !== STATIC_CACHE && cacheName !== DYNAMIC_CACHE) {
              console.log('[SW] Deleting old cache:', cacheName)
              return caches.delete(cacheName)
            }
          })
        )
      })
      .then(() => {
        console.log('[SW] Service worker activated')
        return self.clients.claim()
      })
  )
})

// Fetch event - handle requests with different strategies
self.addEventListener('fetch', (event) => {
  const { request } = event
  const url = new URL(request.url)

  // Skip non-GET requests
  if (request.method !== 'GET') {
    return
  }

  // Skip chrome-extension and other non-http requests
  if (!url.protocol.startsWith('http')) {
    return
  }

  // Handle different types of requests
  if (isStaticFile(request)) {
    event.respondWith(cacheFirst(request))
  } else if (isAPIRequest(request)) {
    if (isNetworkFirstAPI(request)) {
      event.respondWith(networkFirst(request))
    } else {
      event.respondWith(staleWhileRevalidate(request))
    }
  } else {
    event.respondWith(networkFirst(request))
  }
})

// Cache first strategy (for static files)
async function cacheFirst(request) {
  try {
    const cachedResponse = await caches.match(request)
    if (cachedResponse) {
      return cachedResponse
    }

    const networkResponse = await fetch(request)
    if (networkResponse.ok) {
      const cache = await caches.open(STATIC_CACHE)
      cache.put(request, networkResponse.clone())
    }
    return networkResponse
  } catch (error) {
    console.error('[SW] Cache first failed:', error)
    return getOfflinePage()
  }
}

// Network first strategy (for dynamic content)
async function networkFirst(request) {
  try {
    const networkResponse = await fetch(request)
    if (networkResponse.ok) {
      const cache = await caches.open(DYNAMIC_CACHE)
      cache.put(request, networkResponse.clone())
    }
    return networkResponse
  } catch (error) {
    console.log('[SW] Network failed, trying cache:', request.url)
    const cachedResponse = await caches.match(request)
    if (cachedResponse) {
      return cachedResponse
    }
    
    // Return offline page for navigation requests
    if (request.mode === 'navigate') {
      return getOfflinePage()
    }
    
    throw error
  }
}

// Stale while revalidate strategy (for API requests)
async function staleWhileRevalidate(request) {
  const cache = await caches.open(DYNAMIC_CACHE)
  const cachedResponse = await cache.match(request)

  const fetchPromise = fetch(request)
    .then((networkResponse) => {
      if (networkResponse.ok) {
        cache.put(request, networkResponse.clone())
      }
      return networkResponse
    })
    .catch((error) => {
      console.log('[SW] Network request failed:', error)
      return cachedResponse
    })

  return cachedResponse || fetchPromise
}

// Get offline page
async function getOfflinePage() {
  const cache = await caches.open(STATIC_CACHE)
  return cache.match('/offline.html') || new Response('Offline', { status: 503 })
}

// Helper functions
function isStaticFile(request) {
  const url = new URL(request.url)
  return url.pathname.match(/\.(js|css|png|jpg|jpeg|gif|svg|ico|woff|woff2|ttf|eot)$/)
}

function isAPIRequest(request) {
  const url = new URL(request.url)
  return url.pathname.startsWith('/api/')
}

function isNetworkFirstAPI(request) {
  return NETWORK_FIRST_PATTERNS.some(pattern => pattern.test(request.url))
}

// Background sync for offline actions
self.addEventListener('sync', (event) => {
  console.log('[SW] Background sync triggered:', event.tag)
  
  if (event.tag === 'background-sync') {
    event.waitUntil(doBackgroundSync())
  }
})

async function doBackgroundSync() {
  try {
    // Get pending actions from IndexedDB
    const pendingActions = await getPendingActions()
    
    for (const action of pendingActions) {
      try {
        await fetch(action.url, {
          method: action.method,
          headers: action.headers,
          body: action.body
        })
        
        // Remove successful action
        await removePendingAction(action.id)
        console.log('[SW] Synced action:', action.id)
      } catch (error) {
        console.error('[SW] Failed to sync action:', action.id, error)
      }
    }
  } catch (error) {
    console.error('[SW] Background sync failed:', error)
  }
}

// Push notification handler
self.addEventListener('push', (event) => {
  console.log('[SW] Push notification received')
  
  const options = {
    body: 'لديك إشعار جديد في نظام نمو',
    icon: '/icons/icon-192x192.png',
    badge: '/icons/badge-72x72.png',
    tag: 'numu-notification',
    data: {
      url: '/'
    },
    actions: [
      {
        action: 'open',
        title: 'فتح التطبيق',
        icon: '/icons/open-24x24.png'
      },
      {
        action: 'dismiss',
        title: 'إغلاق',
        icon: '/icons/close-24x24.png'
      }
    ],
    requireInteraction: true,
    silent: false
  }

  if (event.data) {
    try {
      const data = event.data.json()
      options.body = data.body || options.body
      options.data = { ...options.data, ...data }
    } catch (error) {
      console.error('[SW] Failed to parse push data:', error)
    }
  }

  event.waitUntil(
    self.registration.showNotification('نمو EMS', options)
  )
})

// Notification click handler
self.addEventListener('notificationclick', (event) => {
  console.log('[SW] Notification clicked:', event.action)
  
  event.notification.close()

  if (event.action === 'dismiss') {
    return
  }

  const urlToOpen = event.notification.data?.url || '/'

  event.waitUntil(
    clients.matchAll({ type: 'window', includeUncontrolled: true })
      .then((clientList) => {
        // Check if app is already open
        for (const client of clientList) {
          if (client.url.includes(self.location.origin) && 'focus' in client) {
            client.focus()
            client.navigate(urlToOpen)
            return
          }
        }
        
        // Open new window
        if (clients.openWindow) {
          return clients.openWindow(urlToOpen)
        }
      })
  )
})

// Message handler for communication with main thread
self.addEventListener('message', (event) => {
  console.log('[SW] Message received:', event.data)
  
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting()
  }
  
  if (event.data && event.data.type === 'GET_VERSION') {
    event.ports[0].postMessage({ version: CACHE_NAME })
  }
})

// Utility functions for IndexedDB operations
async function getPendingActions() {
  // Implement IndexedDB operations for offline actions
  return []
}

async function removePendingAction(id) {
  // Implement IndexedDB operations for removing actions
  return true
}

console.log('[SW] Service worker loaded successfully')
