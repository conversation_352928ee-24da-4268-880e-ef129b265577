{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "type-check": "tsc --noEmit", "preview": "vite preview", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:unit": "jest --selectProjects unit", "test:integration": "jest --selectProjects integration", "test:ci": "jest --ci --coverage --watchAll=false", "test:debug": "jest --detect<PERSON><PERSON><PERSON><PERSON><PERSON> --forceExit", "test:update": "jest --updateSnapshot", "test:performance": "jest --testPathPattern=performance", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:headed": "playwright test --headed", "test:visual": "playwright test visual-regression", "test:all": "npm run test:ci && npm run test:e2e", "playwright:install": "playwright install", "analyze-bundle": "node scripts/analyze-bundle.js", "build:prod": "node scripts/build-production.js", "build:analyze": "npm run build && npm run analyze-bundle"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.2", "@reduxjs/toolkit": "^2.8.2", "@tanstack/react-query": "^5.59.0", "@tanstack/react-virtual": "^3.10.8", "@types/uuid": "^10.0.0", "autoprefixer": "^10.4.21", "chart.js": "^4.4.9", "chartjs-adapter-date-fns": "^3.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "framer-motion": "^11.11.17", "fuse.js": "^7.0.0", "lucide-react": "^0.511.0", "postcss": "^8.5.4", "puppeteer-core": "^24.10.0", "react": "^19.1.0", "react-day-picker": "^9.8.0", "react-dom": "^19.1.0", "react-hook-form": "^7.53.2", "react-hot-toast": "^2.5.2", "react-redux": "^9.2.0", "react-router-dom": "^7.6.1", "recharts": "^2.15.3", "tailwind-merge": "^3.3.0", "tailwindcss-animate": "^1.0.7", "zustand": "^5.0.1"}, "devDependencies": {"@eslint/js": "^9.25.0", "@playwright/test": "^1.52.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.1.0", "@testing-library/user-event": "^14.5.2", "@types/jest": "^29.5.8", "@types/jspdf": "^2.0.0", "@types/node": "^22.15.24", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@typescript-eslint/eslint-plugin": "^8.37.0", "@typescript-eslint/parser": "^8.37.0", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.5.1", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.0.0", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-html-reporters": "^3.1.5", "jest-junit": "^16.0.0", "jest-sonar-reporter": "^2.0.0", "jest-watch-typeahead": "^2.2.2", "jspdf": "^3.0.1", "msw": "^2.0.8", "playwright": "^1.52.0", "prettier": "^3.6.2", "rollup-plugin-visualizer": "^6.0.3", "tailwindcss": "^3.4.17", "ts-jest": "^29.1.1", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5", "vite-plugin-compression": "^0.5.1", "xlsx": "^0.18.5"}}